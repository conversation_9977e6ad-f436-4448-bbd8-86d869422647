package com.mam.custom.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.http.entity.FileEntity;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.hibernate.SQLQuery;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mam.custom.OvsConstants;
import com.mam.custom.OvsUtils;
import com.mam.custom.serviceLayer.OvsChannelServices;
import com.mam.custom.serviceLayer.OvsSalesforcePublishServices;
import com.mam.custom.serviceLayer.SalesforceConnectionManager;
import com.mam.custom.serviceLayer.renditions.ChannelMediaGenerator;
import com.mam.custom.serviceLayer.renditions.OvsImageGenerator;
import com.mam.dataLayer.ChannelsDao.PublishingStatus;
import com.mam.dataLayer.HibernateTransactionInterface;
import com.mam.dataLayer.asset.Asset;
import com.mam.dataLayer.asset.AssetTypes;
import com.mam.dataLayer.hibernate.HibernateUtil;
import com.mam.dataLayer.mediaObject.MediaObject;
import com.mam.dataLayer.mediaObject.MediaRoles;
import com.mam.dataLayer.util.QbQuery;
import com.mam.exception.HttpClientException;
import com.mam.serviceLayer.HttpServices;
import com.mam.serviceLayer.ServiceManager;
import com.mam.serviceLayer.TaxonomyServices;
import com.mam.serviceLayer.cloud.azure.AzureStorageServices;

import blade.util.FileUtils;

public class OvsExportUtils {

    private static org.apache.log4j.Logger trace = org.apache.log4j.Logger.getLogger(OvsExportUtils.class);
    private static AzureStorageServices azureServices = new AzureStorageServices();
    static String azureContainerName = ServiceManager.getConfigurationServices()
            .getSystemProperty("azure.containerName", "dam");
    static String azurePrefixPath = ServiceManager.getConfigurationServices().getSystemProperty("azure.prefixPath",
            "output/");
    private static String sftphost;
    private static String sftpusername;
    private static String sftppassword;

    /**
     * Method to initialize variables for sftp connection
     *
     * @param host
     * @param username
     * @param password
     */
    public static void initializeSftpData(String host, String username, String password) {
        sftphost = host;
        sftpusername = username;
        sftppassword = password;
    }

    /**
     * @return
     * @throws Exception
     *
     *                   Query the assets_in_channels table to search for assets to
     *                   publish (that are in PUBLISHING state)
     */
    public static List<Integer> getAssetIdsandPcmChannelIdsForPcm(PublishingStatus publishingStatus,
            int maxProductsToExport) throws Exception {

        return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Integer>>() {

            @SuppressWarnings({ "unchecked" })
            @Override
            public List<Integer> execute() throws Exception {
                List<Integer> assetIds = new ArrayList<>();
                SQLQuery sqlQuery = HibernateUtil.getSession()
                        .createSQLQuery("SELECT assets_in_channels.asset_id, channels.external_id "
                                + "FROM assets_in_channels  "
                                + "INNER JOIN assets ON assets.asset_id = assets_in_channels.asset_id "
                                + "INNER JOIN channels ON channels.id=assets_in_channels.channel_id "
                                + "WHERE channels.channel_type_id = ? " // Channel Type PCM
                                + "AND assets_in_channels.status = ? " // Publishing
                                + "AND asset_type_id = ? " // Products
                                + "ORDER BY assets_in_channels.last_date_updated ASC " // From the oldest
                                + "LIMIT ?");
                sqlQuery.setInteger(0, OvsConstants.CHANNEL_TYPE_PCM_ID);
                sqlQuery.setString(1, publishingStatus.toString());
                sqlQuery.setInteger(2, OvsConstants.PRODUCT_ASSET_TYPE_ID);
                sqlQuery.setInteger(3, maxProductsToExport);

                List<Object[]> result = sqlQuery.list();

                for (Object[] row : result) {
                    Integer assetId = (Integer) row[0];
                    Integer channelId = (Integer) row[1];
                    assetIds.add(assetId);
                    assetIds.add(channelId);

                }

                return assetIds;
            }
        });
    }

    /**
     * @param document
     * @throws IOException
     * @throws Exception
     */
    public static void composeAndSaveXml(Document document, String outputPath, boolean exportToRemote,
            boolean exportToAzure) throws IOException, Exception {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("CET"));
        Date date = new Date();
        String baseFileName = "WH_Medias_" + dateFormat.format(date);
        String filePath = outputPath + File.separatorChar + baseFileName + ".xml";
        String tmpFilePath = outputPath + File.separatorChar + "tmp_" + baseFileName + ".tmp";

        try {
            FileOutputStream fos = new FileOutputStream(tmpFilePath);

            try {
                OutputFormat format = OutputFormat.createPrettyPrint();
                XMLWriter writer = new XMLWriter(fos, format);
                writer.write(document);
                writer.flush();
                fos.close();
            } finally {
                fos.close();
            }

            boolean success = new File(tmpFilePath).renameTo(new File(filePath));
            // UPLOAD FILE TO AZURE STORAGE

            if (exportToRemote) {

                moveXmlToRemoteLocation(exportToAzure, filePath);
            }

            if (!success) {
                throw new Exception("Failed to rename file " + tmpFilePath);
            }

        } catch (Exception e) {
            trace.error("Cannot write file " + filePath, e);
            throw e;
        }
    }

    private static void moveXmlToRemoteLocation(boolean exportToAzure, String filePath) throws Exception {
        if (exportToAzure) {
            azureServices.uploadFile(azureContainerName, "/" + azurePrefixPath, new File(filePath),
                    new HashMap<String, Object>());
        } else {
            ServiceManager.getStorageServices().sftpPutFile(sftphost, sftpusername, 22, sftppassword,
                    new File(filePath), "/" + azurePrefixPath, true, new File(filePath).getName());
        }
    }

    /**
     * @param productsRootElement
     * @param assetId
     * @throws NumberFormatException
     * @throws Exception
     */
    public static boolean addProductToXml(Element productsRootElement, Integer assetId, ArrayList<Integer> channelsIds)
            throws NumberFormatException, Exception {

        boolean zalandoOnly = false;
        boolean imagePublishOnError = false;

        try {
            JsonObject metadataOfAsset = ServiceManager.getMetadataServices().getAssetMetadata(assetId.toString());
            String productCode = "";
            String colorGroupCode = "";
            OvsChannelServices chanServ = new OvsChannelServices();
            boolean isLegacy = OvsUtils.getIsLegacyById(Integer.toString(assetId));

            if (metadataOfAsset.has("product_code")) {
                productCode = metadataOfAsset.get("product_code").getAsString();
            }

            if (metadataOfAsset.has("color_group_code") && metadataOfAsset.get("color_group_code") != null) {
                colorGroupCode = metadataOfAsset.get("color_group_code").toString();
            }

            Element productXml = DocumentHelper.createElement("product");
            productXml.addAttribute("marketing_description", "");
            String idProduct = productCode;

            if (metadataOfAsset.has("erp_id")) {
                if (metadataOfAsset.get("erp_id").equals("null")) {
                    idProduct = metadataOfAsset.get("erp_id").getAsString();
                }
            }

            productXml.addAttribute("id", idProduct);

            if (metadataOfAsset.has("notify_pim_shooting_season") && metadataOfAsset.has("shootingYear") && metadataOfAsset.has("shootingPeriod")) {
                Boolean notifyPimShootingSeason = metadataOfAsset.get("notify_pim_shooting_season").getAsBoolean();
                if (notifyPimShootingSeason.equals(true)) {
                    String shootingYear = metadataOfAsset.get("shootingYear").getAsString();
                    String shootingPeriod = metadataOfAsset.get("shootingPeriod").getAsString();
                    productXml.addAttribute("shooting_year", shootingYear);
                    productXml.addAttribute("shooting_period", shootingPeriod);
                }
            }

            for (Integer channelId : channelsIds) {

                String channelGlobalId = chanServ.getChannelGlobalIdByChanneExternalId(channelId);

                // if (channelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_ZALANDO)) {
                //     // canale zalando non deve pi� essere esportato, ma comunque risulter� PUBLISHED
                //     if (channelsIds.size() == 1) {
                //         zalandoOnly = true;
                //     }
                //     _createZalandoRenditions(assetId, channelId);
                //     continue;
                // }

                String catalogueTermId = chanServ.getCatalogueIdByExternalChannelId(String.format("%04d", channelId));
                String catalogueString = OvsUtils.getTaxonomyMasterDescriptionCachedByTermId(catalogueTermId);
                Element channel = DocumentHelper.createElement("channel");
                channel.addAttribute("id", String.format("%04d", channelId));
                channel.addAttribute("catalog", catalogueString);
                Element images = DocumentHelper.createElement("images");

                List<Object[]> infoForXml = getApprovedImagesForProduct(assetId, isLegacy);
                // Devo controllare se c'e', e qual e', un immagine adatta per generare la
                // thumbnail del canale ovs.it

                //String suitableImageForChannelThumbnail = _getSuitableImageForChannel(channelId, infoForXml);

                // Devo prendermi altezza e taglia della modella dello shooting
                if (ServiceManager.getConfigurationServices().getSystemProperty("ecomm.enableSizeWeight", "false")
                        .equals("true")) {
                    trace.info("Adding Size and Height for asset #" + assetId);
                    _addSizeHeightToImages(images, assetId);
                }

                for (Object[] imageInfo : infoForXml) {
                    Integer src_asset_id = (Integer) imageInfo[0];
                    Integer ordinal = (Integer) imageInfo[1];
                    String shotType = imageInfo[2].toString();
                    String taxId = TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE);
                    String shotId = TaxonomyServices.getTaxonomyTermIdByExternalId(taxId, shotType);
                    String imageChannel = chanServ.getChannelExternalIdByShotTypeId(shotId);
                    String imageChannelGlobalId = chanServ.getChannelGlobalIdByShotTypeId(shotId);
                    // Gestione offset

                    ordinal = OvsUtils.calculateBaseOrderValue(ordinal, imageChannelGlobalId);

                    if (Integer.parseInt(imageChannel) == channelId) {
                        Element image = DocumentHelper.createElement("image");
                        Asset imageAsset = ServiceManager.getAssetServices().getAsset(src_asset_id);
                        int catalogueId = OvsUtils.getCatalogueIdFromImage(src_asset_id);

                        // Generate all media needed for PCM if not exists

                        ChannelMediaGenerator channelMediaGenerator = new ChannelMediaGenerator();
                        Map<String, Object> transcodingOptions = new HashMap<>();
                        transcodingOptions.put(OvsImageGenerator.CATALOGUE_ID_OPTION_NAME, catalogueId);
                        String imageChannelforRenditionGeneration = imageChannelGlobalId;
                        if (imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_SCHOOL) ||
                                imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_STEFANEL) ||
                                imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_GAP) ||
                                imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_PIOMBO) ||
                                imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_LES_COPAINS)) {

                            imageChannelforRenditionGeneration = OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT;
                        }

                        if (imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_CROFF)) {
                            imageChannelforRenditionGeneration = OvsConstants.CHANNEL_GLOBAL_ID_UPIM;
                        }

                        // Generate mobile and desktop rendition if photo shot type is compatible

                        // if (suitableImageForChannelThumbnail != null
                        //         && imageAsset.getAssetId().equals(suitableImageForChannelThumbnail)) {
                        //     ArrayList<String> extraProfiles = new ArrayList<String>();
                        //     // extraProfiles.add(OvsConstants.MOBILE_RENDITION_ROLE_NAME);
                        //     extraProfiles.add(OvsConstants.DESKTOP_RENDITION_ROLE_NAME);
                        //     transcodingOptions.put("extra_media_profiles", extraProfiles);
                        // }

                        try {
                            channelMediaGenerator.generateMedia(OvsConstants.CHANNEL_TYPE_PCM_ID.toString(),
                                    imageChannelforRenditionGeneration, imageAsset, transcodingOptions);
                        } catch (Exception e) {
                            trace.error("Error generating media for asset " + assetId, e);
                            throw e;
                        }

                        // Se canale UPIM su catalogo OVS, bisogna generare esplicitamente le rendition
                        // if (imageChannelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_UPIM) &&
                        // productCatalogue.equalsIgnoreCase(OvsConstants.OVS_CATALOGUE_EXTERNAL_ID)) {
                        //
                        // try {
                        // channelMediaGenerator.generateMedia(OvsConstants.CHANNEL_TYPE_PCM_ID.toString(),
                        // OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT, imageAsset, transcodingOptions);
                        // } catch (Exception e) {
                        //
                        // _setProductToErrorPublishing(assetId, channelsIds);
                        // throw e;
                        // }
                        // }

                        image.addAttribute("image_type", shotType);
                        image.addAttribute("order", ordinal != null ? ordinal.toString() : null);

                        Element renditionsElt = DocumentHelper.createElement("renditions");
                        image.add(renditionsElt);

                        // TODO verificare se possiamo prenderli direttamente dal DB
                        String[] ecommerceMediaRoleNames = new String[] { OvsConstants.ECOMMERCE_RENDITION_ROLE_NAME };

                        // if (imageChannelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT)) {

                        //     ecommerceMediaRoleNames = new String[] {
                        //             OvsConstants.ECOMMERCE_RENDITION_ROLE_NAME,
                        //             // OvsConstants.JPEG_RENDITION_ROLE_NAME,
                        //             // OvsConstants.H476JPEG_RENDITION_ROLE_NAME,
                        //             // OvsConstants.H220JPEG_RENDITION_ROLE_NAME,
                        //             // OvsConstants.H111JPEG_RENDITION_ROLE_NAME,
                        //             // OvsConstants.H50JPEG_RENDITION_ROLE_NAME
                        //     };
                        // } else {

                        //     ecommerceMediaRoleNames = new String[] { OvsConstants.ECOMMERCE_RENDITION_ROLE_NAME,
                        //             // OvsConstants.H111JPEG_RENDITION_ROLE_NAME
                        //         };
                        // }

                        for (String mediaRoleName : ecommerceMediaRoleNames) {
                            Element renditionElt = DocumentHelper.createElement("rendition");
                            renditionsElt.add(renditionElt);
                            Integer mediaRoleId = MediaRoles.toId(mediaRoleName);
                            MediaObject mo = imageAsset.getMediaObjectByMediaRoleId(mediaRoleId);

                            if (mo == null) {
                                throw new Exception("Unexpected null media object for media role:" + mediaRoleName
                                        + ". Check the channel_media_configuration table.");
                            }

                            String _url = mo.getClientUrl();

                            if (_url == null) {
                                throw new Exception("Unexpected null URL for media role:" + mediaRoleName);
                            }

                            renditionElt.addAttribute("rendition_id", mediaRoleName);
                            renditionElt.addAttribute("url", _url);
                            renditionElt.addAttribute("mime_type", "image/jpeg");

                            // l'id del nodo <image> lo prendiamo dalla parte finale della URL per la
                            // rendition ecommerce. Questo meccanismo tiene conto anche della migrazione e
                            // non va cambiato.
                            try{
                            trace.debug("Checking if WebDav publishing is required for MediaRoleId: " + mediaRoleId);
                            if (mediaRoleId == OvsConstants.ECOMMERCE_RENDITION_ROLE_ID) {
                                int idx = _url.lastIndexOf('/');

                                if (idx > -1) {
                                    String fileName = _url.substring(idx + 1);
                                    String ecommImageAssetId = FileUtils.getFileBaseName(fileName);
                                    image.addAttribute("id", ecommImageAssetId);
                                } else {
                                    image.addAttribute("id", "");
                                    trace.error("Product #" + idProduct + " has malformed eCommerce URL!");
                                }

                                if (imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_STEFANEL)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_GAP)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_PIOMBO)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_LES_COPAINS)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_UPIM)
                                        || imageChannelGlobalId.equalsIgnoreCase(OvsConstants.CHANNEL_GLOBAL_ID_CROFF)) {

                                    String filepath = mo.getResourcePath("share");
                                    File file = new File(filepath);
                                    if (file.exists()){
                                        Boolean result = file.delete();
                                        trace.debug("Eliminazione file dal filepath " + filepath + ".Esito: " + result);
                                    }
                                    mo.setIsOnline(false);
                                    ServiceManager.getMediaObjectServices().update(mo);
                                    filepath = mo.getResourcePathSmart();
                                    trace.info("Publishing " + filepath);
                                    imagePublishOnError = !OvsSalesforcePublishServices.publishLocalFileToSalesforce(
                                            filepath, metadataOfAsset, mo, shotType)
                                            || imagePublishOnError;
                                    trace.info(
                                            imagePublishOnError ? "Failed publishing " + filepath : "Asset published");
                                }
                            }
                        }catch (Exception e) {
                            trace.error("Error publishing image on WebDav ", e);
                            imagePublishOnError = true;
                        }
                        }
                        images.add(image);
                    }
                }

                // Qui avviene l'aggiunta all'xml della nuova rendition mobile
                // if (ServiceManager.getConfigurationServices().getSystemProperty("ecomm.enableNewRendition", "false")
                //         .equals("true")) {
                //     _addThumbnailsToChannel(colorGroupCode, suitableImageForChannelThumbnail, images);
                // }

                channel.add(images);

                // Videos
                channelGlobalId = chanServ.getChannelGlobalIdByChanneExternalId(channelId);
                if (channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_STEFANEL)
                        || channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_GAP)
                        // || channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_PIOMBO)
                        || channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT)
                        || channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_LES_COPAINS)
                        || channelGlobalId.equals(OvsConstants.CHANNEL_GLOBAL_ID_UPIM)) {
                    // Videos
                    Object[] result = getVideoForProduct(assetId);

                    if (result != null) {

                        Integer videoId = (Integer) result[0];
                        String videoGlobalId = (String) result[1];
                        Element videos = DocumentHelper.createElement("videos");
                        Element video = DocumentHelper.createElement("video");
                        Element videoRenditions = DocumentHelper.createElement("renditions");
                        Element videoRendition = DocumentHelper.createElement("rendition");
                        video.addAttribute("id", videoId.toString());
                        video.addAttribute("video_resource_id", videoGlobalId);
                        MediaObject videoMo = ServiceManager.getMediaObjectServices()
                                .getMediaObjectByAssetIdAndRoleId(videoId.toString(), MediaRoles.ESSENCE);
                        videoRendition.addAttribute("url", videoMo.getClientUrl());
                        videoRendition.addAttribute("rendition_id", "ecommerce");

                        String mimeType = "video/mp4";

                        if (!videoMo.getFileSuffix().equals("mp4")) {

                            switch (videoMo.getFileSuffix()) {

                                case "mov":
                                    mimeType = "video/quicktime";
                                    break;

                                case "avi":
                                    mimeType = "video/x-msvideo";
                                    break;

                                case "wmv":
                                    mimeType = "video/x-ms-wmv";
                                    break;

                                default:
                                    break;
                            }
                        }

                        videoRendition.addAttribute("mime_type", mimeType);
                        videoRenditions.add(videoRendition);
                        video.add(videoRenditions);
                        videos.add(video);
                        channel.add(videos);
                    }
                }
                Element shopByLook = DocumentHelper.createElement("shop_by_look");
                shopByLook.addAttribute("id", productCode + "_" + channelId);
                trace.debug("Adding shop by look products for asset #" + assetId);

                Integer channelDbId = chanServ.getChannelIdByChannelExternalId(channelId);

                addShopByLookProducts(shopByLook, assetId, channelDbId);
                Boolean hasShopByLookElements= shopByLook.elements("product_code").size() > 0;
                if (hasShopByLookElements) {
                    channel.add(shopByLook);
                }
                productXml.add(channel);
            }



            //if (!zalandoOnly) {
            saveXmlForAudit(productXml, productCode);

            productsRootElement.add(productXml);
            ///}

            if (imagePublishOnError) {
                throw new Exception("Error publishing image on WebDav");
            }

            return true;
        } catch (Exception e) {
            trace.error("Error generating XML for product " + assetId, e);
            // if max publication retries reached, set product to error
            // per ogni canale su cui era in publishing, incrementa tentativi e se raggiunge il massimo setta in errore
            setProductToErrorPublishing(assetId);
            return false;
        }
    }


    public static void _addSizeHeightToImages(Element images, Integer assetId) throws Exception {

        JsonObject productMetadata = ServiceManager.getMetadataServices().getAssetMetadata(assetId.toString());
        JsonElement size = productMetadata.get("new_size");
        JsonElement height = productMetadata.get("new_height");

        if (!size.isJsonNull() && !height.isJsonNull()) {

            String sizeLabel = OvsUtils.getProductSizeLabel(assetId.toString(), size.getAsString());

            if (null == sizeLabel || sizeLabel.equalsIgnoreCase("")) {
                // size label null significa che non c'è match, quindi il valore ottenuto non è
                // un codice o i codici non sono stati aggiunti al prodotto in fase di
                // immissione
                // lasciamo quindi model_size_code vuoto, passiamo la label normalmente
                // quindi --> size: label della taglia inserita dall'utente e non matchata

                images.addAttribute("model_size_code", sizeLabel);
                images.addAttribute("model_height", height.getAsString());
                images.addAttribute("model_size", size.getAsString());
            } else {
                // abbiamo trovato un match per il codice taglia e l'asset, size quindi contiene
                // il codice taglia, sizeLabel conterrà la label associata
                images.addAttribute("model_size_code", size.getAsString());
                images.addAttribute("model_height", height.getAsString());
                images.addAttribute("model_size", sizeLabel);
            }

            // Aggiorno current
            productMetadata.add("current_height", height);
            productMetadata.add("current_size", size);
            ServiceManager.getMetadataServices().saveAssetMetadata(assetId.toString(), productMetadata);
        }
    }


    public static void addShopByLookProducts(Element shopByLook, Integer assetId, Integer channelId) throws Exception {

        List<Object[]> relatedProducts = getRelatedProducts(assetId, channelId);

        for (Object[] relatedProduct : relatedProducts) {
            String relatedProdCode = relatedProduct[0].toString();
            Element relatedProd = DocumentHelper.createElement("product_code");
            relatedProd.addAttribute("product_code", relatedProdCode);

            shopByLook.add(relatedProd);
        }
    }

    private static List<Object[]> getRelatedProducts(Integer assetId, Integer channelId) throws Exception {

        return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {

            @SuppressWarnings("unchecked")
            @Override
            public List<Object[]> execute() throws Exception {


                SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                    "select cm.product_code, al.src_asset_id from common_metadata cm "
                    + "join asset_links al on al.src_asset_id = cm.asset_id "
                    + "where al.dst_asset_id = ? "
                    + "and al.link_type = 'generic_link' and al.extra1 = 'shop_by_look_in_ch#" + channelId + "'; ");
                sqlQuery.setInteger(0, assetId);

                return sqlQuery.list();
            }
        });
    }

    public static String _getSuitableImageForChannel(Integer channelId, List<Object[]> infoForXml) throws Exception {

        String suitableImageAssetId = null;
        String fallbackImageAssetId = null;
        int position = 100;
        int order = 1000;

        String ovsChannelExternalId = new OvsChannelServices()
                .getChannelExternalIdByChannelGlobalId(OvsConstants.CHANNEL_GLOBAL_ID_OVS_IT);
        Integer ovsChannelExternalIdInt = Integer.valueOf(ovsChannelExternalId);
        String[] suitableShotTypes = OvsConstants.MOBILE_RENDITION_SHOT_TYPE_ORDER.split(",");

        if (ovsChannelExternalIdInt.equals(channelId)) {

            for (int i = 0; i < infoForXml.size(); i++) {

                Integer imageAssetId = (Integer) infoForXml.get(i)[0];
                Integer imageOrder = (Integer) infoForXml.get(i)[1];
                String shotType = (String) infoForXml.get(i)[2];

                // Gestione fallback, prendo la prima se dovesse servire
                if (imageOrder < order) {
                    order = imageOrder;
                    fallbackImageAssetId = imageAssetId.toString();
                }

                // Cerco la suitable
                for (int k = 0; k < suitableShotTypes.length; k++) {

                    if (suitableShotTypes[k].equals(shotType)) {

                        if (position > k) {

                            suitableImageAssetId = imageAssetId.toString();
                            position = k;
                        }

                    }
                }

            }
        }

        // Se non ho trovato immagini suitable, prendo la fallback

        if (suitableImageAssetId == null) {

            if (fallbackImageAssetId != null) {
                return fallbackImageAssetId;
            }
        }
        return suitableImageAssetId;
    }

    public static void _addThumbnailsToChannel(String colorGroupCode, String suitableImageAssetId, Element images)
            throws Exception {

        String desktopUrl = "";
        MediaObject desktopMo = null;

        if (suitableImageAssetId != null) {

            Asset suitableImageAsset = ServiceManager.getAssetServices().getAsset(suitableImageAssetId);
            desktopMo = suitableImageAsset.getMediaObjectByMediaRoleId(OvsConstants.DESKTOP_RENDITION_ROLE_ID);
            desktopUrl = desktopMo != null ? desktopMo.getClientUrl() : "";

            Element desktopImage = DocumentHelper.createElement("image");

            desktopImage.addAttribute("id", "");
            desktopImage.addAttribute("image_type", "M");
            desktopImage.addAttribute("order", "500");

            Element desktopRenditions = DocumentHelper.createElement("renditions");
            desktopImage.add(desktopRenditions);
            Element desktopRendition = DocumentHelper.createElement("rendition");
            desktopRenditions.add(desktopRendition);

            desktopRendition.addAttribute("rendition_id", "ecommerce");
            desktopRendition.addAttribute("url", desktopUrl);
            desktopRendition.addAttribute("mime_type", "image/jpeg");

            images.add(desktopImage);
        }
        // else {
        //
        // //Questa parte non � stata ufficialmente approvata
        // if (!colorGroupCode.equals("")) {
        //
        // String colorAssetId = _getColorAssetIdByColorGroupCode(colorGroupCode);
        // desktopMo =
        // ServiceManager.getMediaObjectServices().getMediaObjectByAssetIdAndRoleId(colorAssetId,
        // OvsConstants.DESKTOP_RENDITION_ROLE_ID);
        // }
        // }
    }

    private String _getColorAssetIdByColorGroupCode(String colorGroupCode) {

        try {
            QbQuery query = new QbQuery(
                    "select common_metadata.asset_id from common_metadata INNER JOIN assets on common_metadata.asset_id = assets.asset_id where asset_subtype=? AND color_group_code = ?",
                    new String[] { OvsConstants.IMAGE_COLOR_SUBTYPE, colorGroupCode });
            HashMap<String, Object> uniqueResult = query.getUniqueResult();

            if (uniqueResult != null) {
                Object assetIdObj = uniqueResult.get("asset_id");
                return assetIdObj.toString();
            } else {
                return null;
            }

        } catch (Exception e) {
            trace.error("Error in _getMiniatureAssetIdByColorGroupCode", e);
            return null;
        }
    }

    public static void setProductToErrorPublishing(Integer assetId, ArrayList<Integer> channelIds) {

        // Metto in errore solo il canale che mi ha dato problemi, l'latro rimane in
        // publishing e al prossimo giro verr� sistemato

        for (Integer channelId : channelIds) {

            try {
                HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {
                    @Override
                    public String execute() throws Exception {
                        // Update assets in channels
                        SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                                "UPDATE assets_in_channels as a SET status = ?, last_date_updated = ? FROM channels as c WHERE a.channel_id=c.id AND a.asset_id = ? and a.status=? AND c.external_id = ?");
                        int i = 0;
                        sqlQuery.setString(i++, PublishingStatus.ERROR_PUBLISHING.toString());
                        sqlQuery.setTimestamp(i++, new Date(System.currentTimeMillis()));
                        sqlQuery.setInteger(i++, assetId);
                        sqlQuery.setString(i++, OvsConstants.PRODUCT_PUBLISHING_STATUS_NAME);
                        sqlQuery.setInteger(i++, channelId);
                        int nRes = sqlQuery.executeUpdate();

                        if (nRes != 1) {
                            trace.warn("Expecting 1 record updated, actual value is " + nRes);
                        }

                        return null;
                    }
                });
            } catch (Exception e) {
                trace.error("Error in _setProductToErrorPublishing: " + e);
            }
        }
    }

    public static void setProductToErrorPublishing(Integer assetId) {

        // Metto tutto PCM in errore senza distinzione di canale

        try {
            HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {
                @Override
                public String execute() throws Exception {
                    // Update assets in channels
                    SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "UPDATE assets_in_channels as aic " +
                            " SET status = ?, last_date_updated = ? " +
                            " FROM channels as c " +
                            " WHERE aic.channel_id=c.id " +
                            " AND aic.publication_retries_count < ( " +
                                " SELECT cast(value_field as integer) from system_registry " +
                                " where key_field = 'maxPublicationRetries' ) " +
                            " AND aic.asset_id = ? " +
                            " and aic.status=? " +
                            " AND c.channel_type_id = ?");
                    int i = 0;
                    sqlQuery.setString(i++, PublishingStatus.ERROR_PUBLISHING.toString());
                    sqlQuery.setTimestamp(i++, new Date(System.currentTimeMillis()));
                    sqlQuery.setInteger(i++, assetId);
                    sqlQuery.setString(i++, OvsConstants.PRODUCT_PUBLISHING_STATUS_NAME);
                    sqlQuery.setInteger(i++, OvsConstants.CHANNEL_TYPE_PCM_ID);
                    int nRes = sqlQuery.executeUpdate();

                    if (nRes != 1) {
                        trace.warn("Expecting 1 record updated, actual value is " + nRes);
                    }

                    return null;
                }
            });
        } catch (Exception e) {
            trace.error("Error in _setProductToErrorPublishing: " + e);
        }

    }

    /**
     * Salva l'xml relativo ad un singolo prodotto sotto la cartella
     * /media/workarea/pcm-exports
     * per poter successivamente ricostruire cosa e' stato mandato al PCM.
     * NOTA: c'e' un cleaner che svuota questa cartella tenendo solo gli ultimi N
     * giorni
     */
    public static void saveXmlForAudit(Element productXml, String productCode) {
        try {

            String outDirPath = ServiceManager.getConfigurationServices().getWorkareaDirPath() + "/pcm-exports";
            new File(outDirPath).mkdirs();
            String outFilePath = outDirPath + "/prod_" + productCode + ".xml";

            FileOutputStream fos = new FileOutputStream(outFilePath);

            try {
                OutputFormat format = OutputFormat.createPrettyPrint();
                XMLWriter writer = new XMLWriter(fos, format);
                writer.write(productXml);
                writer.flush();
                fos.close();
            } finally {
                fos.close();
            }
        } catch (Exception ex) {
            trace.error("Unable to save audit XML", ex);
            // NOTA: non rilanciamo l'eccezione perche' altrimenti bloccheremmo l'export
            // principale
        }
    }

    /**
     * @param assetIdToChannelIdMap
     * @throws Exception
     *
     *                   Set assets_in_channels.status=PUBLISHED for all exported
     *                   assetIds
     */
    public static void setAssetsToPublished(HashMap<Integer, ArrayList<Integer>> assetIdToChannelIdMap)
            throws Exception {
        String assetIdsStr = "";

        for (Integer assetId : assetIdToChannelIdMap.keySet()) {

            if (assetIdsStr.length() > 0) {
                assetIdsStr += ",";
            }

            assetIdsStr += assetId;
        }

        final String assetIdsStrFinal = assetIdsStr;

        if (assetIdToChannelIdMap.keySet().size() > 0) {

            for (Integer assetId : assetIdToChannelIdMap.keySet()) {
                ArrayList<Integer> channelIds = assetIdToChannelIdMap.get(assetId);

                for (Integer externalChannelId : channelIds) {
                    HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {
                        @Override
                        public String execute() throws Exception {
                            // Update assets in channels
                            SQLQuery sqlQuery = HibernateUtil.getSession()
                                    .createSQLQuery(
                                            "UPDATE assets_in_channels as a SET status = ?, last_date_updated = ? FROM channels as c WHERE a.channel_id=c.id AND a.asset_id = ? and a.status=? AND c.external_id = ?");
                            int i = 0;
                            sqlQuery.setString(i++, OvsConstants.PRODUCT_PUBLISHED_STATUS_NAME);
                            sqlQuery.setTimestamp(i++, new Date(System.currentTimeMillis()));
                            sqlQuery.setInteger(i++, assetId);
                            sqlQuery.setString(i++, OvsConstants.PRODUCT_PUBLISHING_STATUS_NAME);
                            sqlQuery.setInteger(i++, externalChannelId);
                            int nRes = sqlQuery.executeUpdate();

                            if (nRes != 1) {
                                trace.warn("Expecting 1 record updated, actual value is " + nRes);
                            }

                            SQLQuery sqlQueryDeadlines = HibernateUtil.getSession()
                                    .createSQLQuery(
                                            "UPDATE products_channel_deadlines_status as pcds " +
                                            " SET pubblicato_actual_date = now() " +
                                            " FROM channels as c WHERE c.id = pcds.channel_id " +
                                            " AND c.external_id = ? " +
                                            " AND pcds.asset_id = ?");

                            //sqlQueryDeadlines.setTimestamp(0, new Date(System.currentTimeMillis()));
                            sqlQueryDeadlines.setInteger(0, externalChannelId);
                            sqlQueryDeadlines.setInteger(1, assetId);
                            nRes = sqlQueryDeadlines.executeUpdate();

                            if (nRes != 1) {
                                trace.warn("Expecting 1 record updated, actual value is " + nRes);
                            }


                            return null;
                        }
                    });
                    OvsExportUtils.setAssetsInChannelsInYearsAndPeriodsStatus(assetId, externalChannelId, OvsConstants.PRODUCT_PUBLISHED_STATUS_NAME);
                }
            }

            HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {

                @Override
                public String execute() throws Exception {
                    // Update common metadata
                    SQLQuery sqlQuery1 = HibernateUtil.getSession()
                            .createSQLQuery("UPDATE common_metadata SET validation_timestamp=NOW() WHERE asset_id IN ("
                                    + assetIdsStrFinal + ")");
                    sqlQuery1.executeUpdate();

                    return null;
                }
            });

            // Invalidate Cache
            String[] assetIds = OvsUtils.toStringArray(
                    assetIdToChannelIdMap.keySet());
            ServiceManager.getAssetServices().invalidateAndReindex(assetIds);
        }
    }

    public static Element createPartXml(Element productXml, String element, String elementValue,
            JsonObject assetMetadataInChannelList, Boolean taxonomy) throws NumberFormatException, Exception {
        try {
            if (taxonomy) {
                if (assetMetadataInChannelList.has(elementValue)) {
                    String taxonomyTermId = assetMetadataInChannelList.get(elementValue).toString().replace("[", "")
                            .replace("]", "");
                    if (TaxonomyServices.getTaxonomyTermById(taxonomyTermId) != null) {
                        String valueToAddXml = TaxonomyServices.getTaxonomyTermById(taxonomyTermId)
                                .get("masterDescription").toString();
                        if (valueToAddXml != null) {
                            Element voidElement = DocumentHelper.createElement(element);
                            productXml.add(voidElement);
                        }
                    }
                } else {
                    productXml.addElement(element);
                    trace.info("Asset not have name in this attribute: " + elementValue);
                }
            } else {
                if (assetMetadataInChannelList.has(elementValue)) {
                    if (assetMetadataInChannelList.get(elementValue).equals("null")) {
                        Element voidElement = DocumentHelper.createElement(element);
                        productXml.add(voidElement);
                    } else {
                        productXml.addElement(element).addText(assetMetadataInChannelList.get(elementValue).toString());
                    }
                } else {
                    Element voidElement = DocumentHelper.createElement(element);
                    productXml.add(voidElement);
                }
            }
        } catch (Exception e) {
            trace.error("Error when creat part of xml");
            throw e;
        }
        return null;
    }

    public static List<Object[]> getApprovedImagesForProduct(Integer assetId, boolean isLegacy) throws Exception {

        if (isLegacy) {
            return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {

                @SuppressWarnings("unchecked")
                @Override
                public List<Object[]> execute() throws Exception {
                    // aggiungere la clausola temporale nella select + limite +
                    // publishing
                    // e se sono prodotti
                    List<Object[]> assetIdInChannelList = null;
                    int taxonomyId = Integer
                            .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE));
                    SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "SELECT src_asset_id,ordinal,external_id FROM asset_links "
                            + "inner join common_metadata on common_metadata.asset_id=asset_links.src_asset_id "
                            + "INNER JOIN assets_in_taxonomy_terms ON asset_links.src_asset_id=assets_in_taxonomy_terms.asset_id "
                            + "INNER JOIN taxonomy_terms ON taxonomy_terms.id=assets_in_taxonomy_terms.term_id "
                            + "WHERE dst_asset_id = ? "
                            + "and common_metadata.state="+ OvsConstants.DIGITAL_APPROVED_STATUS
                            + " AND taxonomy_id = ? ORDER BY ordinal");
                    sqlQuery.setInteger(0, assetId);
                    sqlQuery.setInteger(1, taxonomyId);
                    assetIdInChannelList = sqlQuery.list();

                    return assetIdInChannelList;
                }
            });
        } else {
            return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {

            @SuppressWarnings("unchecked")
            @Override
            public List<Object[]> execute() throws Exception {
                // aggiungere la clausola temporale nella select + limite +
                // publishing
                // e se sono prodotti
                List<Object[]> assetIdInChannelList = null;
                int taxonomyId = Integer
                        .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE));
                SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                        "SELECT distinct(src_asset_id),ordinal,external_id FROM asset_links "
                        + "inner join shooting_metadata on shooting_metadata.asset_id=asset_links.src_asset_id "
                        + "INNER JOIN assets_in_taxonomy_terms ON asset_links.src_asset_id=assets_in_taxonomy_terms.asset_id "
                        + "INNER JOIN taxonomy_terms ON taxonomy_terms.id=assets_in_taxonomy_terms.term_id "
                        + "WHERE dst_asset_id = ? "
                        + "AND cast(shooting_metadata.metadata->>'shot_status' as text) = ? "
                        + " AND taxonomy_id = ? ORDER BY ordinal");
                sqlQuery.setInteger(0, assetId);
                sqlQuery.setString(1,  OvsConstants.SHOT_STATUS_APPROVATA);
                sqlQuery.setInteger(2, taxonomyId);
                assetIdInChannelList = sqlQuery.list();

                return assetIdInChannelList;
            }
        });

        }


    }

    public static Object[] getVideoForProduct(Integer assetId) throws Exception {
        return (Object[]) HibernateUtil.doTransaction(() -> {
            SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                    "SELECT src_asset_id, global_asset_id FROM asset_links inner join assets on assets.asset_id = asset_links.src_asset_id WHERE asset_type_id = :assetTypeId and dst_asset_id = :dstAssetId LIMIT 1");
            sqlQuery.setInteger("assetTypeId", Integer.parseInt(AssetTypes.VIDEO_ASSET_ID));
            sqlQuery.setInteger("dstAssetId", assetId);
            return sqlQuery.uniqueResult();
        });
    }

    public static String createWebdavCompleteName(Integer assetId, String shotType, String webDavUrl,
            String salesforcePath, MediaObject mo) throws Exception {
        JsonObject metadata = ServiceManager.getMetadataServices().getAssetMetadata(assetId.toString());

        String prodCode = metadata.get("product_code").getAsString();
        String name = prodCode + "_" + shotType + "." + mo.getMediaType().getFileSuffix();

        String subfolderFilename = prodCode.substring(0, 3) + "/" + prodCode.substring(3, 6) + "/";
        String completeName = webDavUrl.trim() + salesforcePath + subfolderFilename + name;
        return completeName;
    }

    public static void publishOnWebDav(FileEntity fileEntity, String completeName) throws Exception {
        try {
            SalesforceConnectionManager CM = SalesforceConnectionManager.getSalesForceConnectionManager();
            String token = CM.getValidBearerToken();
            HttpServices hS = ServiceManager.getHttpServices();
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);
            hS.httpPut(completeName, fileEntity, headers, 201);
            trace.info("Resource " + completeName + " created");

        } catch (HttpClientException httpClientException) {
            int statusCode = httpClientException.getStatusCode();

            if (statusCode == 401) {

                trace.info("Retrying " + completeName);

                // Token expired for some reason
                SalesforceConnectionManager CM = SalesforceConnectionManager.getSalesForceConnectionManager();
                CM.setNewAuthData();
                String token = CM.getValidBearerToken();

                HttpServices hS = ServiceManager.getHttpServices();
                HashMap<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);

                try {
                    hS.httpPut(completeName, fileEntity, headers, 201);
                } catch (Exception exception) {
                    trace.error("Some error occurred uploading file to Salesforce after token expired", exception);
                    throw new Exception("Some error occurred uploading file to Salesforce after token expired");
                }

                trace.info("Resource " + completeName + " created");
            }
        }
    }

    /**
     *
     * @param assetId     the asset id, will be parsed to Integer
     * @param isPublished 1 if the asset is correctly published on webdav, 0
     *                    otherwise
     * @throws Exception
     */
    public static void setAssetToPublishedOnWebdavTable(String assetId, Integer isPublished, String salesforceUrl) throws Exception {
        HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {

            @Override
            public String execute() throws Exception {
                SQLQuery sqlQuery = HibernateUtil.getSession()
                        .createSQLQuery("Select id from webdav_publishing WHERE asset_id = ?");
                sqlQuery.setInteger(0, Integer.valueOf(assetId));
                Object rowObject = sqlQuery.uniqueResult();

                if (rowObject == null) {
                    sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "INSERT INTO public.webdav_publishing(asset_id, published_status, last_publish_date, salesforce_url) VALUES (?, ?, now(), ?)");
                    sqlQuery.setInteger(0, Integer.valueOf(assetId));
                    sqlQuery.setInteger(1, isPublished);
                   // sqlQuery.setTimestamp(2, new java.sql.Timestamp(System.currentTimeMillis()));
                    sqlQuery.setString(2, salesforceUrl);
                } else {
                    sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "UPDATE webdav_publishing SET published_status =? , last_publish_date=now() , salesforce_url = ? WHERE asset_id = ?");
                    sqlQuery.setInteger(0, isPublished);
                    //sqlQuery.setTimestamp(1, new java.sql.Timestamp(System.currentTimeMillis()));

                    sqlQuery.setString(1, salesforceUrl);
                    sqlQuery.setInteger(2, Integer.valueOf(assetId));
                }

                sqlQuery.executeUpdate();
                return null;
            }
        });
    }

    public static List<Object[]> getApprovedImagesForProductOnChannel(Integer assetId, Integer channelId, boolean isLegacy)
            throws Exception {
        if (isLegacy){
            return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {
                @SuppressWarnings("unchecked")
                @Override
                public List<Object[]> execute() throws Exception {
                    // aggiungere la clausola temporale nella select + limite +
                    // publishing
                    // e se sono prodotti
                    List<Object[]> assetIdInChannelList = null;
                    SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                        "SELECT src_asset_id, ordinal, external_id, external_id_type "
                        + "FROM asset_links al "
                        + "INNER JOIN common_metadata cm ON cm.asset_id = al.src_asset_id "
                        + "INNER JOIN assets_in_taxonomy_terms aitt ON al.src_asset_id = aitt.asset_id "
                        + "INNER JOIN taxonomy_terms tt ON tt.id = aitt.term_id "
                        + "INNER JOIN shot_types_in_channels stic ON stic.shot_type_id = aitt.term_id "
                        + "WHERE dst_asset_id = :dstAssetId "
                        + "AND cm.state = " + OvsConstants.DIGITAL_APPROVED_STATUS
                        + "AND stic.channel_id = :channelId "
                        + "ORDER BY ordinal"
                    );
                    sqlQuery.setParameter("dstAssetId", assetId);
                    sqlQuery.setParameter("channelId", channelId);
                    assetIdInChannelList = sqlQuery.list();

                    return assetIdInChannelList;
                }
            });
        }
        else {
            return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {
                @SuppressWarnings("unchecked")
                @Override
                public List<Object[]> execute() throws Exception {
                    List<Object[]> assetIdInChannelList = null;
                    int taxonomyId1 = Integer
                            .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE));
                    int taxonomyId2 = Integer
                            .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_SHOOTING_CATEGORY));
                    SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "SELECT DISTINCT al.src_asset_id, al.ordinal, tt1.external_id AS external_id_1, tt2.external_id AS external_id_2 "
                            + "FROM asset_links al "
                            + "INNER JOIN shooting_metadata sm ON sm.asset_id = al.src_asset_id "
                            + "INNER JOIN assets_in_taxonomy_terms aitt ON al.src_asset_id = aitt.asset_id "
                            + "INNER JOIN taxonomy_terms tt1 ON tt1.id=aitt.term_id "
                            + "INNER JOIN shot_types_in_channels stic ON stic.shot_type_id = aitt.term_id "
                            + "INNER JOIN assets_in_taxonomy_terms aitt2 ON aitt2.asset_id = al.src_asset_id "
                            + "INNER JOIN taxonomy_terms tt2 ON tt2.id = aitt2.term_id "
                            + "WHERE al.dst_asset_id = :dstAssetId "
                            + "AND cast(sm.metadata->>'shot_status' as text) = :status "
                            + "AND stic.channel_id = :channelId "
                            + "AND tt1.taxonomy_id = :taxonomyId1 "
                            + "AND tt2.taxonomy_id = :taxonomyId2 "
                            + "ORDER BY al.ordinal"
                        );
                    sqlQuery.setParameter("dstAssetId", assetId);
                    sqlQuery.setParameter("status",  OvsConstants.SHOT_STATUS_APPROVATA);
                    sqlQuery.setParameter("channelId", channelId);
                    sqlQuery.setParameter("taxonomyId1", taxonomyId1);
                    sqlQuery.setParameter("taxonomyId2", taxonomyId2);
                    assetIdInChannelList = sqlQuery.list();

                    return assetIdInChannelList;
                }
            });
        }
    }

    public static List<Object[]> getImagesForProductOnChannelTargetLegacy(Integer assetId, Integer channelId)
            throws Exception {
        return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {

            @SuppressWarnings("unchecked")
            @Override
            public List<Object[]> execute() throws Exception {
                // aggiungere la clausola temporale nella select + limite +
                // publishing
                // e se sono prodotti
                List<Object[]> assetIdInChannelList = null;
                int taxonomyId = Integer
                        .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE));
                SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                        "SELECT DISTINCT src_asset_id, ordinal, external_id "
                                + "FROM asset_links "
                                + "INNER JOIN common_metadata ON common_metadata.asset_id = asset_links.src_asset_id "
                                + "INNER JOIN assets_in_taxonomy_terms ON asset_links.src_asset_id = assets_in_taxonomy_terms.asset_id "
                                + "INNER JOIN taxonomy_terms ON taxonomy_terms.id = assets_in_taxonomy_terms.term_id "
                                + "WHERE dst_asset_id = ? "
                                + "AND taxonomy_id = ? "
                                + "AND state = ? "
                                + "AND external_id IN (SELECT external_id FROM taxonomy_terms tt WHERE id IN (SELECT shot_type_id FROM shot_types_in_channels stic WHERE stic.channel_id = ?)) "
                                + "ORDER BY ordinal");
                sqlQuery.setInteger(0, assetId);
                sqlQuery.setInteger(1, taxonomyId);
                sqlQuery.setInteger(2, OvsConstants.DIGITAL_APPROVED_STATUS);
                sqlQuery.setInteger(3, channelId);
                assetIdInChannelList = sqlQuery.list();

                return assetIdInChannelList;
            }
        });
    }

    public static List<Object[]> getImagesForProductOnChannelTargetNonLegacy(Integer assetId, Integer channelId)
            throws Exception {
        return HibernateUtil.doTransaction(new HibernateTransactionInterface<List<Object[]>>() {

            @SuppressWarnings("unchecked")
            @Override
            public List<Object[]> execute() throws Exception {

                List<Object[]> assetIdInChannelList = null;
                int taxonomyId = Integer
                        .parseInt(TaxonomyServices.getTaxonomyIdByName(OvsConstants.TAXONOMY_PHOTO_SHOT_TYPE));
                SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                        "SELECT DISTINCT src_asset_id, ordinal, external_id "
                                + "FROM asset_links "
                                + "INNER JOIN common_metadata ON common_metadata.asset_id = asset_links.src_asset_id "
                                + "INNER JOIN assets_in_taxonomy_terms ON asset_links.src_asset_id = assets_in_taxonomy_terms.asset_id "
                                + "INNER JOIN taxonomy_terms ON taxonomy_terms.id = assets_in_taxonomy_terms.term_id "
                                + "INNER JOIN shooting_metadata ON shooting_metadata.asset_id = common_metadata.asset_id "
                                + "WHERE dst_asset_id = ? "
                                + "AND taxonomy_id = ? "
                                + "AND CAST(shooting_metadata.metadata as jsonb)->>'shot_status' = ? "
                                + "AND external_id IN (SELECT external_id FROM taxonomy_terms tt WHERE tt.id IN (SELECT shot_type_id FROM shot_types_in_channels stic WHERE stic.channel_id = ?)) "
                                + "ORDER BY ordinal");
                sqlQuery.setInteger(0, assetId);
                sqlQuery.setInteger(1, taxonomyId);
                sqlQuery.setParameter(2, OvsConstants.SHOT_STATUS_APPROVATA);
                sqlQuery.setInteger(3, channelId);
                assetIdInChannelList = sqlQuery.list();

                return assetIdInChannelList;
            }
        });
    }

    public static void setPublishDate(HashMap<Integer, ArrayList<Integer>> assetIdToChannelIdMap) throws Exception {

        if (assetIdToChannelIdMap.keySet().size() > 0) {

            for (Integer assetId : assetIdToChannelIdMap.keySet()) {
                ArrayList<Integer> channelIds = assetIdToChannelIdMap.get(assetId);

                for (Integer channelId : channelIds) {
                    Integer newchannelId = Integer.parseInt(OvsChannelServices.getChannelIdByExternalId(channelId));

                    if (!OvsUtils.getIsLegacyById(Integer.toString(assetId))) {

                        try {
                            HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {
                                @Override
                                public String execute() throws Exception {

                                    // Set the publishing date for the alert system
                                    SQLQuery sqlQuery = HibernateUtil.getSession()
                                            .createSQLQuery(
                                                    "UPDATE products_channel_deadlines_status SET pubblicato_actual_date = ? "
                                                     + "WHERE asset_id = ? AND channel_id = ?");
                                    int i = 0;
                                    sqlQuery.setTimestamp(i++, new Date());
                                    sqlQuery.setInteger(i++, assetId);
                                    sqlQuery.setInteger(i++, newchannelId);

                                    sqlQuery.executeUpdate();

                                    return null;
                                }
                            });
                            ServiceManager.getAssetServices().invalidateAndReindex(String.valueOf(assetId));
                        } catch (Exception e) {
                            trace.error("Error when trying to insert the publishing date", e);
                            throw e;
                        }
                    }
                }
            }
        }
    }

    /**
     * Attempts to set the status of `assets_in_channels_in_years_and_periods` to the provided {@code status}
     * for the given asset and channel (identified by an external channel ID). The logic is:
     * <ol>
     *   <li>Fetch the current (year, period) from the channel's external ID.</li>
     *   <li>Find the channel's internal ID from the external ID.</li>
     *   <li>Retrieve all rows (assetInChannelsId, yearAndPeriodId, year, period) for the asset & channel.</li>
     *   <li>If the current "YYYY_P" season is present among them, update that row to the given {@code status}.</li>
     *   <li>If not present, compute [previous, next] seasons. If one of those is present, update its row.</li>
     * </ol>
     *
     * @param assetId          The ID of the asset.
     * @param externalChannelId The channel's external ID (used to get the current season and the internal channelId).
     * @param status           The status value to set (e.g., "PUBLISHED", "WAITING_FOR_PHOTOS", etc.).
     * @throws Exception If any database or lookup error occurs.
     */
    public static void setAssetsInChannelsInYearsAndPeriodsStatus(int assetId,
                                                                            int externalChannelId,
                                                                            String status) throws Exception {

        // 1) Get current year & period from externalChannelId
        ArrayList<String> currentYearAndPeriod = OvsUtils.getCurrentYearAndPeriodByChannelExternalId(externalChannelId);
        if (currentYearAndPeriod.isEmpty()) {
            // No "current" season found => we cannot proceed
            trace.info("No current shooting_year/period found for externalChannelId=" + externalChannelId);
            return;
        }

        // Example: currentYearAndPeriod => ["2024", "1"]
        int currentYear   = Integer.parseInt(currentYearAndPeriod.get(0));
        int currentPeriod = Integer.parseInt(currentYearAndPeriod.get(1));
        String currentSeasonStr = currentYear + "_" + currentPeriod;

        // 2) Convert externalChannelId -> channelId
        Integer channelId = OvsChannelServices.getChannelIdByChannelExternalId(externalChannelId);
        if (channelId == null) {
            trace.info("No channel found for externalChannelId=" + externalChannelId + ". Aborting update.");
            return;
        }

        // 3) Retrieve all rows for (assetId, channelId)
        //    Each row: [assetInChannelsId, yearAndPeriodId, year, period]
        List<Object[]> definitions = OvsUtils.getYearsAndPeriodsForChannelAsset(assetId, channelId);
        if (definitions.isEmpty()) {
            trace.info("No year/period definitions found for assetId=" + assetId + ", channelId=" + channelId);
            return;
        }

        // 4) Check if currentSeasonStr (e.g. "2024_1") is present
        Object[] matchingRow = findRowByYearPeriod(definitions, currentSeasonStr);
        if (matchingRow == null) {
            // If not found => compute previous/next
            List<String> prevNext = OvsAutomaticChannelImportUtils.getPreviousAndNextSeasons(currentYear, currentPeriod);
            // e.g. if current=1 => ["(year-1)_2", "(year)_2"]

            for (String candidate : prevNext) {
                matchingRow = findRowByYearPeriod(definitions, candidate);
                if (matchingRow != null) {
                    break; // Found either previous or next
                }
            }
        }

        // 5) If we found a row, update to the given `status`
        if (matchingRow != null) {
            // matchingRow => [assetInChannelsId, yearAndPeriodId, year, period]
            int assetInChannelsId = (int) matchingRow[0];
            int yearAndPeriodId   = (int) matchingRow[1];
            int foundYear         = (int) matchingRow[2];
            int foundPeriod       = (int) matchingRow[3];

            String foundSeasonStr = foundYear + "_" + foundPeriod;

            // Save status => user-supplied `status`
            OvsUtils.saveAssetsInChannelsInYearsAndPeriodsStatus(
                assetInChannelsId,
                yearAndPeriodId,
                status
            );
            trace.info("Updated season " + foundSeasonStr + " to '" + status + "' "
                    + "(assetInChannelsId=" + assetInChannelsId
                    + ", yearAndPeriodId=" + yearAndPeriodId + ")");
        } else {
            trace.info("No matching (current/previous/next) season found among definitions => nothing updated.");
        }
    }

    /**
     * Utility method to search for a row whose "year_period" matches a target string.
     *
     * @param rows               A list of Object[] each in form [assetInChannelsId, yearAndPeriodId, year, period].
     * @param desiredYearPeriod  The target season string (e.g. "2024_1").
     * @return The matching row or null if none found.
     */
    private static Object[] findRowByYearPeriod(List<Object[]> rows, String desiredYearPeriod) {
        for (Object[] row : rows) {
            Integer rowYear   = (Integer) row[2];
            Integer rowPeriod = (Integer) row[3];
            String rowSeason  = rowYear + "_" + rowPeriod;

            if (rowSeason.equals(desiredYearPeriod)) {
                return row;
            }
        }
        return null; // not found
    }


}