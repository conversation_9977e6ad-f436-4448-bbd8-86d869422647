package com.mam.custom.activeService;

import java.io.FileReader;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.hibernate.SQLQuery;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.mam.custom.OvsConstants;
import com.mam.custom.serviceLayer.OvsIamConnectionServices;
import com.mam.dataLayer.HibernateTransactionInterface;
import com.mam.dataLayer.Roles;
import com.mam.dataLayer.asset.Asset;
import com.mam.dataLayer.asset.AssetTypes;
import com.mam.dataLayer.hibernate.HibernateUtil;
import com.mam.dataLayer.user.User;
import com.mam.dataLayer.user.Users;
import com.mam.dataLayer.util.QbQuery;
import com.mam.exception.InfrastructureException;
import com.mam.serviceLayer.LoggingServicesInterface;
import com.mam.serviceLayer.MetadataServices;
import com.mam.serviceLayer.ServiceManager;
import com.mam.serviceLayer.SystemRegistryServices;
import com.mam.serviceLayer.TimerActiveServiceBase;

/**
 * This class is used to export XML containing image media URLs to hybris for all publiched products.
 *
 */
public class IamStoreImportActiveService extends TimerActiveServiceBase {
	private Logger trace = Logger.getLogger(IamStoreImportActiveService.class);
	private boolean isOvsTestMode = false;

	@SuppressWarnings("rawtypes")
	public void initialize(Map props) {
		if (ServiceManager.getConfigurationServices().getSystemProperty("ovs.testMode", "false").equals("true")) {
			isOvsTestMode = true;
		}
	}

	@Override
	public void tickEvent() throws Exception {

		if (this.isShuttingDown()) {
			return;
		}

		Date nowDate = new Date();

		// In debug mode lavoriamo ad alta frequenza, altrimenti questo servizio deve lavorare con frequenza giornaliera
		if (!ServiceManager.getConfigurationServices().isDebugMode()) {
			// Verifica che l'orario corrente corrisponda all'orario impostato nel system registry a cui il servizio deve lavorare
			Calendar calendar = GregorianCalendar.getInstance();
			calendar.setTime(nowDate);
			int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
			int importStartHour = ServiceManager.getSystemRegistryServices().getIntegerValue("iam.importStartHour", 8);

			if (currentHour != importStartHour) {
				return;
			}
		}

		trace.info("Starting to process IAM Delta");

		try {
			String token = null;
			String response = new OvsIamConnectionServices().connectToIam();
			JsonObject parsedResponse = new JsonParser().parse(response).getAsJsonObject();
			String status = parsedResponse.get("status").getAsString();

			if (status.equals("ok")) {
				token = parsedResponse.get("token").getAsString();
				trace.info("Succesfully connected to IAM");

				// // SPROCESSING STORE DELTA
				String service = ServiceManager.getConfigurationServices().getSystemProperty("iam.StoresDeltaImportService");
				String lastStoresDeltaUpdateDateString = new SystemRegistryServices().getValue(OvsConstants.LAST_STORE_DELTA_UPDATE_KEY);
				DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
				String todayDateString = dateFormat.format(nowDate);
				service = service + "&from=" + lastStoresDeltaUpdateDateString + "&to=" + todayDateString;
				String storesDeltaResponse = new OvsIamConnectionServices().sendIamRequest(service, token);
				JsonArray storesArray = new JsonParser().parse(storesDeltaResponse).getAsJsonArray();
				// JsonParser parser = new JsonParser();
				// JsonArray a = parser.parse(new FileReader("/Users/<USER>/Desktop/stores.json")).getAsJsonArray();
				boolean correctlyProcessed = saveStores(storesArray);
				ServiceManager.getSystemRegistryServices().setValue(OvsConstants.LAST_STORE_DELTA_UPDATE_KEY, todayDateString);

				if (correctlyProcessed) {
					trace.info("Stores Delta correctly processed");
					ServiceManager.getSystemRegistryServices().setValue(OvsConstants.LAST_IAM_STORE_UPDATE_COMPLETED, todayDateString);
				} else{
					throw new Exception("Error updating stores");
				}

			} else {
				//throw new Exception("Error authenticating to IAM, service says: " + parsedResponse.get("msg").getAsString());
			}

		} catch (Exception e) {
			trace.error("Error in IamStoreImportActiveService : ", e);
			ServiceManager.getLoggingServices().error(LoggingServicesInterface.CLASS_APPLICATION, "Exception importing Stores from IAM", e.getMessage());
		}

		trace.info("Finished processing IAM Delta");
	}

	/**
	 * Ritorna true se e' andato tutto bene, false se non e' riuscito a completare l'operazione, es. per system shutdown
	 */
	private boolean saveStores(JsonArray storesArray) {
		long delayBetweenStoresMs = ServiceManager.getConfigurationServices()
				.getIntegerSystemProperty("iam.delayBetweenStoresMs", 1000);
		boolean isOk = true;
		int arraySize = storesArray.size();
		JsonObject currentItem = null;
		for (int i = 0; i < arraySize; i++) {

			if (this.isShuttingDown()) {
				trace.warn("Shutting down importer: import may be only partial");
				isOk = false;
				break;
			}

			currentItem = storesArray.get(i).getAsJsonObject();
			String companyRef = currentItem.get("companyRef").getAsString();
			String enteAziendale1 = currentItem.get("enteAziendale1").getAsString();
			if (companyRef.equals(OvsConstants.IAM_OVS_COMPANY) && !enteAziendale1.equals(OvsConstants.IAM_ENTE_AZIENDALE_UPIM)) {
				String lastOperation = currentItem.get("lastOperation").getAsString();
				String storeAssetId = null;
				String storeCode = currentItem.get("codNegozio").getAsString();
				MetadataServices metadataServices = new MetadataServices();
				try {
					if (lastOperation.equals(OvsConstants.IAM_RECORD_MODIFY)) {
						storeAssetId = getStore(storeCode, currentItem);
						JsonObject storeMetadata = metadataServices.getAssetMetadata(storeAssetId).getAsJsonObject();
						String damStoreStatus = storeMetadata.get("state").getAsString();
						String jsonStoreStatus = currentItem.get("statoApertura").getAsString();
						// se lo stato del json è diverso dallo stato sul DAM e lo stato da JSON è chiuso (5) -> recupero task e request collegate ed elimino
						if (!jsonStoreStatus.equals(damStoreStatus) && jsonStoreStatus.equals(OvsConstants.IAM_STORE_STATUS_CLOSED)) {
							cleanupClosedStore(Integer.valueOf(storeAssetId));
						}
						setStoreMetadata(storeAssetId, storeCode, currentItem);
					} else if (lastOperation.equals(OvsConstants.IAM_RECORD_ADD) || lastOperation.equals(OvsConstants.IAM_RECORD_DELETE) || lastOperation.equals(OvsConstants.IAM_RECORD_RESTORE) || lastOperation.equals(OvsConstants.IAM_RECORD_RESET)) {
						storeAssetId = getOrCreateStore(storeCode, currentItem);
						JsonObject storeMetadata = metadataServices.getAssetMetadata(storeAssetId).getAsJsonObject();
						String oldStoreStatus = storeMetadata.get("state").getAsString();
						String newStoreStatus = currentItem.get("statoApertura").getAsString();
						// se lo stato del json è diverso dallo stato sul DAM e lo stato da JSON è chiuso (5) -> recupero task e request collegate ed elimino
						if (!newStoreStatus.equals(oldStoreStatus) && newStoreStatus.equals(OvsConstants.IAM_STORE_STATUS_CLOSED)) {
							cleanupClosedStore(Integer.valueOf(storeAssetId));
						}
						setStoreMetadata(storeAssetId, storeCode, currentItem);
					}

					if (ServiceManager.isShuttingDown()) {
						isOk = false;
					}

					Thread.sleep(delayBetweenStoresMs);
				} catch (Exception e) {
					trace.error("Some error occurred updating stores metadata", e);
					trace.error("Error data: " + storeCode);
					isOk = false;
					// catch eccezione e continuiamo con altri stores
				}
			}
		}
		return isOk;
	}

	/**
	 * Cleanup store data when store is closed.
	 * Deletes asset links and tasks related to the store.
	 *
	 * @param storeAssetId the asset ID of the store to clean up
	 */
	private void cleanupClosedStore(Integer storeAssetId) {
		try {

			HibernateUtil.doTransaction(() -> {

				// Request: vengono eliminati gli asset link src_asset_id = store_asset_id
				SQLQuery deleteAssetLinks = HibernateUtil.getSession().createSQLQuery(
					"DELETE FROM asset_links " +
					"WHERE src_asset_id = :storeId"
				);

				deleteAssetLinks.setParameter("storeId", storeAssetId);
				deleteAssetLinks.executeUpdate();

				// Task: eliminare asset che in common metadata ha store_id = store_asset_id
				SQLQuery taskIdsQuery = HibernateUtil.getSession().createSQLQuery(
					"SELECT cm.asset_id " +
					"FROM common_metadata cm " +
					"JOIN assets a ON a.asset_id = cm.asset_id " +
					"WHERE cm.store_id = :storeId " +
					"AND a.asset_subtype = :taskSubtype"
				);
				taskIdsQuery.setParameter("storeId", storeAssetId);
				taskIdsQuery.setParameter("taskSubtype", OvsConstants.TYPE_VM_TASK);

				@SuppressWarnings("unchecked")
				List<Integer> taskIds = taskIdsQuery.list();

				for (Integer taskId : taskIds) {
					deleteAssetLinks(taskId);
				}

				return null;
			});

			trace.info("Cleanup completato");
		} catch (Exception e) {
			trace.error("Errore nel cleanup degli store", e);
		}
	}

	/**
	 * Deletes the asset link.
	 */
	private void deleteAssetLinks(Integer assetId) {
		try {
			HibernateUtil.doTransaction(() -> {
				// Delete asset links
				SQLQuery deleteAssetLinks = HibernateUtil.getSession().createSQLQuery(
					"DELETE FROM asset_links WHERE src_asset_id = :assetId"
				);
				deleteAssetLinks.setParameter("assetId", assetId);
				deleteAssetLinks.executeUpdate();
				return null;
			});
		} catch (Exception e) {
			trace.error("Error deleting asset links for asset ID: " + assetId, e);
		}
	}


	private String getStore(String storeCode, JsonObject currentItem) throws Exception {
		String assetId = null;
		assetId = getStoreAssetIdByStoreCode(storeCode);

		if (assetId == null) {
			trace.warn("Store with code " + storeCode + " supposed to exist not found, creating");
			return getOrCreateStore(storeCode, currentItem);
		}
		return assetId;

	}

	private String getOrCreateStore(String storeCode, JsonObject currentItem) {
		String assetId = null;

		try {
			assetId = getStoreAssetIdByStoreCode(storeCode);
			if (assetId == null) {
				Asset asset = ServiceManager.getAssetFactory()
						.createFromTypeName(AssetTypes.BUSINESS_ENTITY_ASSET_NAME);
				asset.setAssetSubtype(OvsConstants.STORE_ASSET_TYPE_NAME);
				asset.setTitle(currentItem.get("descrNegozio").getAsString());
				asset.setCreatedBy(Users.getSystemUserId());
				ServiceManager.getAssetServices().insert(asset);
				assetId = asset.getAssetId();
				String boardUserRoleId = Roles.getRoleIdByName(OvsConstants.BOARD_USER_ROLE_NAME);
				ServiceManager.getAssetServices().addAssetInRole(assetId, boardUserRoleId);
			}

			return assetId;
		} catch (InfrastructureException e) {
			trace.error("Error in getOrCreateStore", e);
			return assetId;
		} catch (Exception e) {
			trace.error("Error in getOrCreateStore", e);
			return assetId;
		}

	}

	private String getStoreAssetIdByStoreCode (String storeCode) {
		String assetId = null;
		ArrayList<HashMap<String, Object>> results = null;
		String sql = "select asset_id " +
				"from common_metadata " +
				"where store_code = " + storeCode;

		QbQuery qbQuery = new QbQuery(sql);

		try {
			results = qbQuery.getResultsList();
		} catch (Exception e) {
			trace.error("Error in getStoreAssetIdByStoreCode", e);
		}
		if (results != null && results.size() != 0) {
			assetId = ((Integer) results.get(0).get("asset_id")).toString();
		}

		return assetId;
	}

	@SuppressWarnings("deprecation")
    private void setStoreMetadata (String storeAssetId, String storeCode, JsonObject currentItem) throws Exception {
		//Associare i metadata
		try {
			JsonObject metadata = new JsonObject();

			metadata.add("store_code", currentItem.get("codNegozio"));
			metadata.add("address", currentItem.get("postalAddress"));
			metadata.add("cap", currentItem.get("postalCode"));
			metadata.add("phone", currentItem.get("telephoneNumber"));
			metadata.add("fax", currentItem.get("facsimileTelNumber"));
			metadata.add("email", currentItem.get("mailNegozio"));
			if (!currentItem.get("areaCommMq").isJsonNull()) {
				metadata.add("mq", currentItem.get("areaCommMq"));
			}
			String openingDate = currentItem.get("dataApertura").getAsString();
			SimpleDateFormat ovsDateFormat = new SimpleDateFormat("dd/MM/yyyy");
			SimpleDateFormat phpFriendlyDateFormat = new SimpleDateFormat("MM/dd/yyyy");
			Date data = ovsDateFormat.parse(openingDate);
			String phpFriendlyOpeningDate = phpFriendlyDateFormat.format(data);
			JsonElement phpFriendlyOpeningDateJson = new JsonPrimitive(phpFriendlyOpeningDate);
			metadata.add("date_opening_date", phpFriendlyOpeningDateJson);
			metadata.add("longitude", currentItem.get("longitude"));
			metadata.add("latitude", currentItem.get("latitude"));
			metadata.add("corporation", currentItem.get("descrSocieta"));
			setMetadataField(currentItem, metadata, "region", "regione");
			metadata.add("city", currentItem.get("l"));
			metadata.add("country", currentItem.get("stato"));
			setMetadataField(currentItem, metadata, "province", "st");
			metadata.add("company", currentItem.get("companyRef"));
			metadata.add("sign", currentItem.get("firma"));
			metadata.add("department", currentItem.get("enteAziendale1"));
			setMetadataField(currentItem, metadata, "cluster", "cluster");
			if (currentItem.get("format").getAsString().equals("")) {
				metadata.add("format", null);
			} else {
				metadata.add("format", currentItem.get("format"));
			}
			metadata.add("division", currentItem.get("enteAziendale1"));
			metadata.add("line", currentItem.get("lineaGest"));
			metadata.add("state", currentItem.get("statoApertura"));
			//Gestione utenti

			if (!currentItem.get("businessData").isJsonNull()) {
				JsonObject businessData = currentItem.get("businessData").getAsJsonObject();
				//Per ogni utente, creo o mi prendo l'utente e lo inserisco nei metadata
				String storeManagerMatr = businessData.get("responsabileMatr").getAsString();
				String storeManagerNameSurname = businessData.get("responsabile").getAsString();
				String storeManagerEmail = businessData.get("responsabileEmail").getAsString();
				String userId = getOrCreateUser(storeManagerMatr, storeManagerNameSurname, storeManagerEmail, false);
				if (userId != null) {
					metadata.add("director", new JsonParser().parse(userId));
					setUserRole(userId, OvsConstants.STORE_MANAGER_ROLE_NAME);
					setUserAsCorporate(userId);
				}
				metadata.add("director_email", businessData.get("storeManagerEmail"));
				String visualPiazzaMatr = businessData.get("visualPiazzaKey").getAsString();
				String visualPiazzaNameSurname = businessData.get("visualPiazza").getAsString();
				String visualPiazzaEmail = businessData.get("visualPiazzaEmail").getAsString();
				userId = getOrCreateUser(visualPiazzaMatr, visualPiazzaNameSurname, visualPiazzaEmail, false);
				if (userId!=null) {
					metadata.add("square_visual", new JsonParser().parse(userId));
					setUserRole(userId, OvsConstants.SQUARE_VISUAL_ROLE_NAME);
					setUserAsCorporate(userId);
				}
				String visualZonaMatr = businessData.get("visualAreaKey").getAsString();
				String visualZonaNameSurname = businessData.get("visualArea").getAsString();
				String visualZonaEmail = businessData.get("visualAreaEmail").getAsString();
				userId = getOrCreateUser(visualZonaMatr, visualZonaNameSurname, visualZonaEmail, false);
                if (userId != null) {

                    metadata.add("zone_visual", new JsonParser().parse(userId));
                    setUserRole(userId, OvsConstants.ZONE_VISUAL_ROLE_NAME);
                    setUserAsCorporate(userId);

                      //Lo ZV si deve portare sempre dietro il delegato
                    String delegatedZoneVisual = _getDelegatedVisualFromZoneVisual(userId);

                    if (delegatedZoneVisual != null) {

                        metadata.add("delegated_visual", new JsonParser().parse(delegatedZoneVisual));
                    } else {

                        metadata.add("delegated_visual", new JsonNull());
                    }
                }
				String areaManagerMatr = businessData.get("areaManagerKey").getAsString();
				String areaManagerNameSurname = businessData.get("areaManager").getAsString();
				String areaManagerEmail = businessData.get("areaManagerEmail").getAsString();
				userId = getOrCreateUser(areaManagerMatr, areaManagerNameSurname, areaManagerEmail, false);
				if (userId != null) {
					metadata.add("manager_area", new JsonParser().parse(userId));
					setUserRole(userId, OvsConstants.AREA_MANAGER_ROLE_NAME);
					setUserAsCorporate(userId);
				}
				String visualRetailMatr = businessData.get("visualRetailKey").getAsString();
				String visualRetailNameSurname = businessData.get("visualRetail").getAsString();
				String visualRetailEmail = businessData.get("visualRetailEmail").getAsString();
				userId = getOrCreateUser(visualRetailMatr, visualRetailNameSurname, visualRetailEmail, false);
				if (userId != null) {
					metadata.add("visual_retail", new JsonParser().parse(userId));
					setUserRole(userId, OvsConstants.VISUAL_RETAIL_ROLE_NAME);
					setUserAsCorporate(userId);
				}


			}

			metadata.add("iam_synch_status", new JsonParser().parse("Synchronized"));

			new MetadataServices().saveAssetMetadata(storeAssetId, metadata, new String[] {"resolveOrInsertTaxonomyMasterDescriptions"});

			//Set board user ACL if needed

			ArrayList <Integer> allowedRoles = new ArrayList<Integer>();
			allowedRoles.addAll(ServiceManager.getAssetServices().getAllowedRoleIds(Integer.valueOf(storeAssetId)));
			String boardUserRoleId = Roles.getRoleIdByName(OvsConstants.BOARD_USER_ROLE_NAME);

			if (! allowedRoles.contains(Integer.valueOf(boardUserRoleId))) {

	            ServiceManager.getAssetServices().addAssetInRole(storeAssetId, boardUserRoleId);
			}

		} catch (Exception e) {
			if (storeAssetId != null) {
				JsonObject metadata = new MetadataServices().getAssetMetadata(storeAssetId).getAsJsonObject();
				metadata.add("iam_synch_status", new JsonParser().parse("Error"));
				new MetadataServices().saveAssetMetadata(storeAssetId, metadata, new String[] { "resolveOrInsertTaxonomyMasterDescriptions" });
			}
			throw e;
		}
	}

	private void setMetadataField (JsonObject currentItem, JsonObject metadata, String metadataField, String ovsField) {
		if (! currentItem.get(ovsField).isJsonNull() && currentItem.get(ovsField) != null && ! currentItem.get(ovsField).getAsString().equals("")) {
			metadata.add(metadataField, currentItem.get(ovsField));
		}
	}

	private String getOrCreateUser(String userName, String nameSurname, String email, boolean isStore) {
		String userId = null;
		if (!userName.equals("") && !nameSurname.equals("") && !email.equals("")) {

			//TRIM 0 INIZIALI SE NON E' STORE
			if (!isStore) {
				userName = userName.replaceFirst("^0+(?!$)", "");
				userName = userName.toLowerCase();
			}

			try {
				User user = ServiceManager.getDirectoryServices().getUserByLoginName(userName);

				if (user == null) {
					user = new User();
					user.setLoginName(userName);
					user.setPassword(ServiceManager.getEncryptionServices().encrypt("VyP.B4ZB$x2\\M<BD"));
					String[] nameSurnameArray = nameSurname.split("\\s+");
					if (nameSurnameArray.length >= 2) {
						user.setFirstName(nameSurnameArray[0]);
						String tempSurname = "";
						for (int i = 1; i < nameSurnameArray.length; i++) {
							tempSurname = tempSurname.concat(" "+nameSurnameArray[i]);
						}

						user.setLastName(tempSurname);

					} else {
						trace.debug("User doesn't have proper name and surname: " + userName);
						throw new Exception(
								"Error reading JSON from IAM: missing name or surname in store's user's data");
					}
					if (!isOvsTestMode) {
						user.setEmail(email);
					}
					user.setEnabled(true);
					userId = new Users().insertUser(user).toString();
				} else {
					userId = user.getUserId();
				}
			} catch (InfrastructureException e) {
				trace.error("Error in getOrCreateUser", e);
			} catch (Exception e) {
				trace.error("Error in getOrCreateUser", e);
			}
		}
		return userId;
	}

	private String setUserRole(String userId, String roleName) {

		try {
			Integer roleId = ServiceManager.getDirectoryServices().getRoleIdByName(roleName);
			ServiceManager.getDirectoryServices().addRoleIdToUserId(Integer.parseInt(userId), roleId);
		} catch (InfrastructureException e) {
			trace.error("Error in setUserRole", e);
		} catch (Exception e) {
			trace.error("Error in setUserRole", e);
		}

		return userId;
	}

	private void setUserAsCorporate(String userId) {

		try {
			HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {

				@Override
				public String execute() throws Exception {
					SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
							"UPDATE users SET is_corporate = 1 WHERE user_id = ?");
					sqlQuery.setInteger(0, Integer.parseInt(userId));
					sqlQuery.executeUpdate();
					return null;
				}
			});
		} catch (Exception e) {
			trace.error("Error in setUserAsCorporate", e);
		}
	}

    private String _getDelegatedVisualFromZoneVisual(String zoneVisualId) {

        try {
            return HibernateUtil.doTransaction(new HibernateTransactionInterface<String>() {

                @Override
                public String execute() throws Exception {
                    SQLQuery sqlQuery = HibernateUtil.getSession().createSQLQuery(
                            "SELECT users.delegated_visual_id from users WHERE user_id = ?");
                    sqlQuery.setInteger(0, Integer.parseInt(zoneVisualId));
                    Object res = sqlQuery.uniqueResult();

                    if (res != null) {

                        return res.toString();
                    } else {

                        return null;
                    }
                }
            });
        } catch (Exception e) {
            trace.error("Error in _getDelegatedVisualFromZoneVisual", e);
            return null;
        }
    }
}
