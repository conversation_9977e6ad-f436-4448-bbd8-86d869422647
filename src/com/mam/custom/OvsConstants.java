package com.mam.custom;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.mam.dataLayer.ChannelsDao.PublishingStatus;

public class OvsConstants {

	public static final String TYPE_VM_TASK = "VmTask";
	public static final Integer CLOSED_REQUEST_VALUE = 4;
	public static final int WAITING_UPLOAD_TASK_VALUE = 1;
	public static final int TO_BE_APPROVED_TASK_VALUE = 2;
	public static final int CLOSED_TASK_VALUE = 3;
	public static final int TO_BE_RESHOOT_TASK_VALUE = 4;
	public static final String TASK_OPEN_LABEL = "Waiting Upload";
    public static final String TASK_TO_BE_APPROVED_LABEL = "To Be Approved";
    public static final String TASK_CLOSED_LABEL = "Closed";
    public static final String TASK_TO_BE_RESHOOT_LABEL = "To Be Reshoot";

	public static final String IAM_STORE_STATUS_CLOSED = "5";

	public static String getTaskStatusLabelFromValue(int value) {
        switch (value) {
            case WAITING_UPLOAD_TASK_VALUE:
                return TASK_OPEN_LABEL;
            case TO_BE_APPROVED_TASK_VALUE:
                return TASK_TO_BE_APPROVED_LABEL;
            case CLOSED_TASK_VALUE:
                return TASK_CLOSED_LABEL;
            case TO_BE_RESHOOT_TASK_VALUE:
                return TASK_TO_BE_RESHOOT_LABEL;
            default:
                return "Unknown Status";
        }
    }

	public static final String TYPE_VM_NOTIFICATION = "VmNotification";
	public static final Integer VM_NOTIFICATION_NOT_APPROVED_IMAGES_EXPIRED_TASK = 0;
	public static final Integer VM_NOTIFICATION_NO_IMAGES_EXPIRED_TASK = 1;
	public static final Integer VM_NOTIFICATION_NEW_TASK = 2;
	public static final Integer VM_NOTIFICATION_REOPENED_TASK = 3;

	public static final String PRODUCT_ASSET_TYPE_NAME = "Product";
	public static final String STYLE_MODEL_ASSET_TYPE_NAME = "StyleModel";
	public static final String STYLE_OPTION_ASSET_TYPE_NAME = "StyleOption";
	public static final String STYLE_PRODUCT_ASSET_TYPE_NAME = "StyleProduct";
	public static final String STORE_ASSET_TYPE_NAME = "Store";
	public static final String SAMPLE_ASSET_TYPE_NAME = "Sample";
	public static final String PARCEL_ASSET_TYPE_NAME = "Parcel";
	public static final String TRACKED_PARCEL_ASSET_TYPE_NAME = "TrackedParcel";
	public static final Integer IMAGE_ASSET_TYPE_ID = 5;
	public static final Integer PRODUCT_ASSET_TYPE_ID = 5900;
	public static final Integer CHANNEL_TYPE_PCM_ID = 1006;
	public static final Integer CHANNEL_TYPE_CACHE_ID = 1007;
	public static final Integer CHANNEL_TYPE_CACHEv2_ID = 1009;
	public static final Integer CHANNEL_TYPE_CLOUD_MEDIA_ID = 1011;
	public static final String CHANNEL_CACHE_V2_GLOBAL_ID = "cachev2";
	public static final Integer CHANNEL_TYPE_CORE = 1008;

	public static final String IMAGE_ECOMMERCE_SUBTYPE = "EcommerceImage";
	public static final String IMAGE_COLOR_SUBTYPE = "ColorImage";
	public static final String IMAGE_VISUAL_SUBTYPE = "VisualImage";
	public static final String IMAGE_STYLE_SUBTYPE = "StyleImage";
	public static final String IMAGE_STYLE_AI_SUBTYPE = "StyleImageAI";
	public static final String IMAGE_MARKETING_SUBTYPE = "MarketingImage";
	public static final String IMAGE_GENERIC_SUBTYPE = "GenericImage";

	public static final Integer DIGITAL_APPROVED_STATUS = 1;
	public static final Integer DIGITAL_REJECTED_STATUS = 2;
	public static final Integer DIGITAL_TO_BE_APPROVED_STATUS = 3;
	public static final Integer DIGITAL_REQUESTED_RESHOOTING_STATUS = 4;

	public static final String SHOT_STATUS_DRAFT = "DRAFT";
	public static final String SHOT_STATUS_DA_APPROVARE = "DA_APPROVARE";
	public static final String SHOT_STATUS_DA_RITOCCARE = "DA_RITOCCARE";
	public static final String SHOT_STATUS_APPROVATA = "APPROVATA";
	public static final String SHOT_STATUS_RIFIUTATA = "RIFIUTATA";

	public static final String SHOOTING_METADATA_SCHEMA_NAME = "shooting";
	public static final String MEMBER_OF_IN_CHANNEL_TYPE = "member_of_in_ch#";

	public static final String TAXONOMY_PHOTO_SHOT_TYPE = "Photo_Shot_Type";
	public static final String TAXONOMY_SHOOTING_CATEGORY = "Shooting_Category";
	public static final String TAXONOMY_CATALOGUES = "Catalogues";
	public static final String CATALOGUE_METADATA_FIELD_NAME = "catalogue";

	public static final String ECOMMERCE_EDITOR_ROLE_NAME = "Ecommerce_Editor";
	public static final String ECOMMERCE_VIEWER_ROLE_NAME = "Ecommerce_Viewer";
	public static final String ECOMMERCE_OVS_VIEWER_ROLE_NAME = "Ecommerce_Ovs_Viewer";
	public static final String ECOMMERCE_UPIM_VIEWER_ROLE_NAME = "Ecommerce_Upim_Viewer";
	public static final String VISUAL_VIEWER_ROLE_NAME = "Visual_Viewer";
	public static final String STYLE_AND_PRODUCT_VIEWER_ROLE_NAME = "Style_And_Product_Viewer";
	public static final String MARKETING_VIEWER_ROLE_NAME = "Marketing_Viewer";
	public static final String MARKETING_EDITOR_ROLE_NAME = "Marketing_Editor";
	public static final String BOARD_USER_ROLE_NAME = "Board_User";

	public static final String OVS_CATALOGUE_EXTERNAL_ID = "ovs";
	public static final String UPIM_CATALOGUE_EXTERNAL_ID = "upim";

	public static final String MOBILE_RENDITION_SHOT_TYPE_ORDER = "F,D1,D2,D3,D4,D5,D6,D7,D8,D9";

	public static final Integer OVS_IMAGE_HEIGHT_FOR_RENDITION = 2600;
	public static final Integer OVS_IMAGE_WIDTH_FOR_RENDITION = 1872;

	public static final Integer UPIM_IMAGE_HEIGHT_FOR_RENDITION = 2380;
	public static final Integer UPIM_IMAGE_WIDTH_FOR_RENDITION = 1920;

	public static final int ECOMMERCE_RENDITION_ROLE_ID = 100;
	public static final int H220JPEG_RENDITION_ROLE_ID = 101;
	public static final int H354JPEG_RENDITION_ROLE_ID = 102;
	public static final int PADWHITE40x40JPEG_RENDITION_ROLE_ID = 103;
	public static final int PADWHITE60x60JPEG_RENDITION_ROLE_ID = 104;
	public static final int XS_RENDITION_ROLE_ID = 105;
	public static final int S_RENDITION_ROLE_ID = 106;
	public static final int M_RENDITION_ROLE_ID = 107;
	public static final int L_RENDITION_ROLE_ID = 108;
	public static final int JPEG_RENDITION_ROLE_ID = 109;
	public static final int MOBILE_RENDITION_ROLE_ID = 110;
	public static final int DESKTOP_RENDITION_ROLE_ID = 111;
	public static final int H476JPEG_RENDITION_ROLE_ID = 112;
	public static final int H111JPEG_RENDITION_ROLE_ID = 113;
	public static final int H50JPEG_RENDITION_ROLE_ID = 114;
	public static final int RESIZED_ESSENCE_RENDITION_ROLE_ID = 115;


	public static final String ECOMMERCE_RENDITION_ROLE_NAME = "ecommerce";
	public static final String H220JPEG_RENDITION_ROLE_NAME = "h220jpeg";
	public static final String H354JPEG_RENDITION_ROLE_NAME = "h354jpeg";
	public static final String PADWHITE40x40JPEG_RENDITION_ROLE_NAME = "padwhite40x40jpeg";
	public static final String PADWHITE60x60JPEG_RENDITION_ROLE_NAME = "padwhite60x60jpeg";
	public static final String XS_RENDITION_ROLE_NAME = "xs";
	public static final String S_RENDITION_ROLE_NAME = "s";
	public static final String M_RENDITION_ROLE_NAME = "m";
	public static final String L_RENDITION_ROLE_NAME = "l";
	public static final String JPEG_RENDITION_ROLE_NAME = "jpeg";
	public static final String MOBILE_RENDITION_ROLE_NAME = "mobile";
	public static final String DESKTOP_RENDITION_ROLE_NAME = "desktop";
	public static final String H476JPEG_RENDITION_ROLE_NAME = "h476jpeg";
	public static final String H111JPEG_RENDITION_ROLE_NAME = "h111jpeg";
	public static final String H50JPEG_RENDITION_ROLE_NAME = "h50jpeg";
	public static final String RESIZED_ESSENCE_RENDITION_ROLE_NAME = "resized-essence";

	public static final Map<Integer, String> CACHE_RENDITIONS_MAP = Collections.unmodifiableMap(
			new HashMap<Integer, String>() {
				{
					put(H220JPEG_RENDITION_ROLE_ID, H220JPEG_RENDITION_ROLE_NAME);
					put(H354JPEG_RENDITION_ROLE_ID, H354JPEG_RENDITION_ROLE_NAME);
					put(PADWHITE40x40JPEG_RENDITION_ROLE_ID, PADWHITE40x40JPEG_RENDITION_ROLE_NAME);
					put(PADWHITE60x60JPEG_RENDITION_ROLE_ID, PADWHITE60x60JPEG_RENDITION_ROLE_NAME);
					put(XS_RENDITION_ROLE_ID, XS_RENDITION_ROLE_NAME);
					put(S_RENDITION_ROLE_ID, S_RENDITION_ROLE_NAME);
					put(M_RENDITION_ROLE_ID, M_RENDITION_ROLE_NAME);
					put(L_RENDITION_ROLE_ID, L_RENDITION_ROLE_NAME);
					put(JPEG_RENDITION_ROLE_ID, JPEG_RENDITION_ROLE_NAME);
				}
			});
	public static final float CATALOGUE_OVS_ASPECT_RATIO = (float) 0.72;
	public static final float CATALOGUE_UPIM_ASPECT_RATIO = (float) 0.80;
	public static final float CHANNEL_ZALANDO_ASPECT_RATIO = (float) 0.69;

	// Product Statuses
	public static final String PRODUCT_WAITING_SHIPMENT = "In attesa di spedizione";
	public static final String PRODUCT_TO_BE_APPROVED_STATUS_NAME = "To Be Approved";
	public static final String PRODUCT_WAITING_FOR_PHOTO_STATUS_NAME = "Waiting for Photos";
	public static final String PRODUCT_SHOOTING_STATUS_NAME = "Shooting";
	public static final String PRODUCT_READY_STATUS_NAME = "Ready For Publish";
	public static final String PRODUCT_PUBLISHING_STATUS_NAME = PublishingStatus.PUBLISHING.toString();
	public static final String PRODUCT_PUBLISHED_STATUS_NAME = "Published";
	public static final String PRODUCT_UNPUBLISHING_STATUS_NAME = PublishingStatus.UNPUBLISHING.toString();
	public static final String PRODUCT_NOT_PUBLISHED_STATUS_NAME = PublishingStatus.NOT_PUBLISHED.toString();
	public static final String PRODUCT_CLOSED_STATUS_NAME = "Closed";
	public static final String PRODUCT_NOT_ACCEPTABLE = "Non Accettabile";
	public static final String PRODUCT_NOT_CONFIRMED = "Non Confermato";

	// Channels global ids

	public static final String CHANNEL_GLOBAL_ID_OVS_IT = "ovs";
	public static final String CHANNEL_GLOBAL_ID_UPIM = "upim";
	public static final String CHANNEL_GLOBAL_ID_ZALANDO = "zalando";
	public static final String CHANNEL_GLOBAL_ID_SCHOOL = "school";
	public static final String CHANNEL_GLOBAL_ID_STEFANEL = "stefanel";
	public static final String CHANNEL_GLOBAL_ID_GAP = "gap";
	public static final String CHANNEL_GLOBAL_ID_PIOMBO = "piombo";
	public static final String CHANNEL_GLOBAL_ID_LES_COPAINS = "lescopains";
	public static final String CHANNEL_GLOBAL_ID_AMAZON = "amazon";
	public static final String CHANNEL_GLOBAL_ID_CROFF = "croff";

	// Channels offsets

	public static final int CHANNEL_OVS_IT_OFFSET = 100;
	public static final int CHANNEL_SCHOOL_OFFSET = 200;
	public static final int CHANNEL_ZALANDO_OFFSET = 300;
	public static final int CHANNEL_UPIM_OFFSET = 400;
	public static final int CHANNEL_STEFANEL_OFFSET = 500;
	public static final int CHANNEL_GAP_OFFSET = 600;
	public static final int CHANNEL_PIOMBO_OFFSET = 700;
	public static final int CHANNEL_LES_COPAINS_OFFSET = 800;
	public static final int CHANNEL_AMAZON_OFFSET = 900;
	public static final int CHANNEL_CROFF_OFFSET = 1000;

	// Assets in Channels.publishing_data special values (used for cache v2)
	public static final String PUBLISHING_DATA_INVALIDATING = "INVALIDATING";
	public static final String PUBLISHING_DATA_INVALIDATED = "INVALIDATED";

	public static final String UPLOAD_DOMAIN_EXTRA_OPTION_NAME = "domain";

	public static final String IAM_OVS_COMPANY = "OVS";
	public static final String IAM_ENTE_AZIENDALE_UPIM = "Divisione Upim";

	public static final String IAM_RECORD_ADD = "ADD";
	public static final String IAM_RECORD_MODIFY = "MODIFY";
	public static final String IAM_RECORD_DELETE = "DELETE";
	public static final String IAM_RECORD_RESTORE = "RESTORE";
	public static final String IAM_RECORD_RESET = "RESET";

	public static final String LAST_STORE_DELTA_UPDATE_KEY = "lastStoresDeltaUpdateDate";
	public static final String LAST_IAM_CSV_UPDATE_KEY = "lastIamCsvUpdateDate";
	public static final String LAST_IAM_STORE_UPDATE_COMPLETED = "lastStoreDeltaUpdateCompleted";

	public static final String STORE_MANAGER_ROLE_NAME = "Store_Manager";
	public static final String STORE_ASSISTANT_ROLE_NAME = "Store_Assistant";
	public static final String SQUARE_VISUAL_ROLE_NAME = "Square_Visual";
	public static final String ZONE_VISUAL_ROLE_NAME = "Zone_Visual";
	public static final String AREA_MANAGER_ROLE_NAME = "Area_Manager";
	public static final String VISUAL_RETAIL_ROLE_NAME = "Visual_Retail";

	// Warehouse API constants
	public static final String DAM_REQUEST_CODE = "3";
	public static final String WAREHOUSE_REQUIRED_QUANTITY = "1";
	public static final Integer MAX_ITERATIONS_GET_STOCK = 10;
	public static final Integer MIN_DAYS_TO_SELLING_TARGET = 10;
	public static final Integer MAX_DAYS_TO_SHIPPING_TARGET = 10;

	// Excel import constants
	public static final String SIZE_HEIGHT_IMPORT = "size_height";
	public static final String CURR_SEASON_PRODUCTS_IMPORT = "curr_season_products";
	public static final String PERIMETER_STORES_IMPORT = "perimeter_stores";

	public static final String SAMPLE_LINK_TYPE_EXTRA_DATA = "product_sample";
	public static final String PARCEL_LINK_TYPE_EXTRA_DATA = "parcel_sample";
	public static final String VM_NOTIFICATION_LINK_TYPE_EXTRA_DATA = "vm_notification_task";
	public static final String STYLE_MODEL_LINK_TYPE_EXTRA_DATA = "style_model";
	public static final String PERIMETER_LINK = "store_perimeter";
	public static final String TRACKED_PARCEL_LINK_TYPE_EXTRA_DATA = "tracked_parcel";
	public static final String INGRESSO_MERCI_TYPE_TRACKING  = "tracking";

	public static final Integer SAMPLE_STATUS_RICHIESTO_A_DEPOSITO = 5;

	// perimeter actions
	public static final int ADD_STORES = 0;
    public static final int REPLACE_STORES = 1;
    public static final int DELETE_STORES = 2;

	// parcel states
	public static final String PARCEL_STATUS_OPEN = "open";
	public static final String PARCEL_STATUS_CLOSED = "closed";

	// warehouse states (stato_rich_trfe)
	public static final Integer WAREHOUSE_STATE_STOCK = -1;
	public static final Integer WAREHOUSE_STATE_INSERITA = 0;
	public static final Integer WAREHOUSE_STATE_LAVORAZIONE = 1;
	public static final Integer WAREHOUSE_STATE_SCARTATA = 2;
	public static final Integer WAREHOUSE_STATE_EVASA_PARZIALMENTE = 3;
	public static final Integer WAREHOUSE_STATE_EVASA = 4;

    public static final String WAREHOUSE_STATE_STOCK_STRING = "Richiesta stock";
    public static final String WAREHOUSE_STATE_INSERITA_STRING = "Inserita";
    public static final String WAREHOUSE_STATE_LAVORAZIONE_STRING = "In lavorazione";
    public static final String WAREHOUSE_STATE_SCARTATA_STRING = "Scartata";
    public static final String WAREHOUSE_STATE_PARZ_EVASA_STRING = "Parzialmente evasa";
    public static final String WAREHOUSE_STATE_EVASA_STRING = "Evasa";

	// Automatic channel import types
	public static final String AUTOMATIC_IMPORT_TYPE = "AUTOMATIC_IMPORT";
	public static final String POSTPRODUCTION_TYPE = "POSTPRODUCTION";

	// Horizontal shot types external_id_type
	public static final String HORIZONTAL_SHOT_EXTERNAL_ID_TYPE = "O";
	public static final String HORIZONTAL_STILL_SHOT_EXTERNAL_ID_TYPE = "OSTILL";

	// Warehouse notification receiver role for incomplete sample
	public static final String NOTIFICATION_RECEIVER_OVS_ROLE_NAME = "Notification_Receiver_OVS";
	public static final String NOTIFICATION_RECEIVER_UPIM_ROLE_NAME = "Notification_Receiver_UPIM";



	public static final Map<String, Integer> WAREHOUSE_STATE_MAP_CHAR_TO_INT = new HashMap<String, Integer>() {{
        put("I", WAREHOUSE_STATE_INSERITA);
        put("W", WAREHOUSE_STATE_LAVORAZIONE);
        put("S", WAREHOUSE_STATE_SCARTATA);
        put("P", WAREHOUSE_STATE_EVASA_PARZIALMENTE);
        put("E", WAREHOUSE_STATE_EVASA);
    }};

	public static final Map<Integer, String> WAREHOUSE_STATE_MAP_INT_TO_STRING = new HashMap<Integer, String>() {{
        put(WAREHOUSE_STATE_STOCK, WAREHOUSE_STATE_STOCK_STRING);
        put(WAREHOUSE_STATE_INSERITA, WAREHOUSE_STATE_INSERITA_STRING);
        put(WAREHOUSE_STATE_LAVORAZIONE, WAREHOUSE_STATE_LAVORAZIONE_STRING);
        put(WAREHOUSE_STATE_SCARTATA, WAREHOUSE_STATE_SCARTATA_STRING);
        put(WAREHOUSE_STATE_EVASA_PARZIALMENTE, WAREHOUSE_STATE_PARZ_EVASA_STRING);
        put(WAREHOUSE_STATE_EVASA, WAREHOUSE_STATE_EVASA_STRING);
    }};

	public static enum LinkingStateErrorCodes {
		NOT_EXISTING_PRODUCT(1), MISSING_EMBEDDED_PARAMETERS(2), PUBLISHED_PRODUCT(3), WRONG_SHOT_TYPE(4);

		int value;

		LinkingStateErrorCodes(int code) {
			this.value = code;
		}

		public int getValue() {
			return value;
		}

	}

	public static enum LinkingState {
		NOT_LINKED(1), LINKED(2);

		int value;

		LinkingState(int code) {
			this.value = code;
		}

		public int getValue() {
			return value;
		}

	}

	public static enum ImageDomain {
		ECOMMERCE(1), VISUAL(2), STYLE_AND_PRODUCT(3), MARKETING(4), GENERIC(5), COLOR(6), STYLE_AND_PRODUCT_AI(7);

		int value;

		ImageDomain(int code) {
			this.value = code;
		}

		public int getValue() {
			return value;
		}

		public static ImageDomain getByValue(int value) {
			ImageDomain domain = null;

			if (value == 1) {
				domain = ImageDomain.ECOMMERCE;
			} else if (value == 2) {
				domain = ImageDomain.VISUAL;
			} else if (value == 3) {
				domain = ImageDomain.STYLE_AND_PRODUCT;
			} else if (value == 4) {
				domain = ImageDomain.MARKETING;
			} else if (value == 5) {
				domain = ImageDomain.GENERIC;
			} else if (value == 6) {
				domain = ImageDomain.COLOR;
			} else if (value == 7) {
				domain = ImageDomain.STYLE_AND_PRODUCT_AI;
			}

			return domain;
		}
	}

	public class ColorGroups {
		public static final String ARANCIONE = "Arancione";
		public static final String ARANCIONE_ID = "1";
		public static final String AZZURRO = "Azzurro";
		public static final String AZZURRO_ID = "2";
		public static final String BEIGE = "Beige";
		public static final String BEIGE_ID = "3";
		public static final String BIANCO = "Bianco";
		public static final String BIANCO_ID = "4";
		public static final String BLU = "Blu";
		public static final String BLU_ID = "5";
		public static final String DENIM = "Denim";
		public static final String DENIM_ID = "6";
		public static final String GIALLO = "Giallo";
		public static final String GIALLO_ID = "7";
		public static final String GRIGIO = "Grigio";
		public static final String GRIGIO_ID = "8";
		public static final String MARRONE = "Marrone";
		public static final String MARRONE_ID = "9";
		public static final String MULTICOLOR = "Multicolor";
		public static final String MULTICOLOR_ID = "10";
		public static final String NERO = "Nero";
		public static final String NERO_ID = "11";
		public static final String ROSA = "Rosa";
		public static final String ROSA_ID = "12";
		public static final String ROSSO = "Rosso";
		public static final String ROSSO_ID = "13";
		public static final String VERDE = "Verde";
		public static final String VERDE_ID = "14";
		public static final String VIOLA = "Viola";
		public static final String VIOLA_ID = "15";

	}

	public static String getColorGroupCode(String value) {
		String colorGroupCode = null;

		switch (value) {
			case ColorGroups.ARANCIONE:
				colorGroupCode = ColorGroups.ARANCIONE_ID;
				break;
			case ColorGroups.AZZURRO:
				colorGroupCode = ColorGroups.AZZURRO_ID;
				break;
			case ColorGroups.BEIGE:
				colorGroupCode = ColorGroups.BEIGE_ID;
				break;
			case ColorGroups.BIANCO:
				colorGroupCode = ColorGroups.BIANCO_ID;
				break;
			case ColorGroups.BLU:
				colorGroupCode = ColorGroups.BLU_ID;
				break;
			case ColorGroups.DENIM:
				colorGroupCode = ColorGroups.DENIM_ID;
				break;
			case ColorGroups.GIALLO:
				colorGroupCode = ColorGroups.GIALLO_ID;
				break;
			case ColorGroups.GRIGIO:
				colorGroupCode = ColorGroups.GRIGIO_ID;
				break;
			case ColorGroups.MARRONE:
				colorGroupCode = ColorGroups.MARRONE_ID;
				break;
			case ColorGroups.MULTICOLOR:
				colorGroupCode = ColorGroups.MULTICOLOR_ID;
				break;
			case ColorGroups.NERO:
				colorGroupCode = ColorGroups.NERO_ID;
				break;
			case ColorGroups.ROSA:
				colorGroupCode = ColorGroups.ROSA_ID;
				break;
			case ColorGroups.ROSSO:
				colorGroupCode = ColorGroups.ROSSO_ID;
				break;
			case ColorGroups.VERDE:
				colorGroupCode = ColorGroups.VERDE_ID;
				break;
			case ColorGroups.VIOLA:
				colorGroupCode = ColorGroups.VIOLA_ID;
				break;
		}
		return colorGroupCode;
	}
}
