<?php
namespace Dam\Profile\Services;

use Dam\Profile\OvsConstants;
use Dam\Profile\UserRoles;
use AssetServices;
use DirectoryServices;

class OvsAclServices {

    public static function setEcommerceViewAclToAsset(string $assetId, string $roleName = null) {
        if ($roleName === null) {
            $roleName = UserRoles::ECOMMERCE_VIEWER;
        }

        $roleId = DirectoryServices::getRoleByName($roleName)->role_id;
        self::setAcl($assetId, $roleId);
    }

    public static function removeEcommerceViewAclToAsset(string $assetId) {

        $roleId=DirectoryServices::getRoleByName(UserRoles::ECOMMERCE_VIEWER)->role_id;
        self::removeAcl($assetId, $roleId);
    }

    public static function setChannelEcommerceViewAclToAssetByExternalId(string $productAssetId, array $externalChannelIds) {
        foreach ($externalChannelIds as $externalChannelId) {
            switch ($externalChannelId) {
                case OvsConstants::OVS_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_OVS_VIEWER);
                    break;
                case OvsConstants::UPIM_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_UPIM_VIEWER);
                    break;
                case OvsConstants::ZALANDO_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_ZALANDO_VIEWER);
                    break;
                case OvsConstants::SCHOOL_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_SCHOOL_VIEWER);
                    break;
                case OvsConstants::STEFANEL_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_STEFANEL_VIEWER);
                    break;
                case OvsConstants::GAP_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_GAP_VIEWER);
                    break;
                case OvsConstants::PIOMBO_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_PIOMBO_VIEWER);
                    break;
                case OvsConstants::LES_COPAINS_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_LES_COPAINS_VIEWER);
                    break;
                case OvsConstants::AMAZON_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_AMAZON_VIEWER);
                    break;
                case OvsConstants::CROFF_CHANNEL_EXTERNAL_ID:
                    self::setEcommerceViewAclToAsset($productAssetId, UserRoles::ECOMMERCE_CROFF_VIEWER);
                    break;
            }
        }
    }


    /**
     * @DEPRECATED
     */
    public static function setEcommerceViewAclToAssetByExternalId(string $productAssetId, string $catalogueExternalId, array $externalChannelIds) {

        if ($catalogueExternalId==OvsConstants::OVS_CATALOGUE_EXTERNAL_ID) {

            //orribile, ma necessario temporaneamente in attesa di revisioni ruoli (PIM non distingue cataloghi OVS e UPIM)
            if( in_array('3085', $externalChannelIds)){
                $roleId=DirectoryServices::getRoleByName(\Dam\Profile\UserRoles::ECOMMERCE_UPIM_VIEWER)->role_id;
            } else {
                $roleId=DirectoryServices::getRoleByName(\Dam\Profile\UserRoles::ECOMMERCE_OVS_VIEWER)->role_id;
            }

        } else if ($catalogueExternalId==OvsConstants::UPIM_CATALOGUE_EXTERNAL_ID) {
            $roleId=DirectoryServices::getRoleByName(UserRoles::ECOMMERCE_UPIM_VIEWER)->role_id;
        }

        self::setAcl($productAssetId, $roleId);
    }

    public static function setEcommerceEditAclToAsset(string $assetId) {

    	$roleId=DirectoryServices::getRoleByName(UserRoles::ECOMMERCE_EDITOR)->role_id;
    	self::setAcl($assetId, $roleId);
    }

    // non usato?
    /*public static function setVisualEditAclToAsset(string $assetId) {

    	$roleId=DirectoryServices::getRoleByName(UserRoles::VISUAL_EDITOR)->role_id;
    	self::setAcl($assetId, $roleId);
    }*/


    public static function setVisualViewAclToAsset(string $assetId) {
    	$roleId=DirectoryServices::getRoleByName(UserRoles::VISUAL_VIEWER)->role_id;
    	self::setAcl($assetId, $roleId);

    }

    public static function setVisualMerchandiseAclToAsset(string $assetId, string $update = 'false') {
    	$roleId=DirectoryServices::getRoleByName(UserRoles::VISUAL_RETAIL)->role_id;
    	self::setAcl($assetId, $roleId, $update);

    }
    public static function setStoreDirectorAclToAsset(string $assetId) {
        $roleId=DirectoryServices::getRoleByName(UserRoles::STORE_MANAGER)->role_id;
    	self::setAcl($assetId, $roleId);

    }
    public static function setStoreAssistantAclToAsset(string $assetId) {
        $roleId=DirectoryServices::getRoleByName(UserRoles::STORE_ASSISTANT)->role_id;
        self::setAcl($assetId, $roleId);

    }
    public static function setVisualAreaAclToAsset(string $assetId) {
    	$roleId=DirectoryServices::getRoleByName(UserRoles::ZONE_VISUAL)->role_id;
    	self::setAcl($assetId, $roleId);
        AssetServices::setLastDateUpdated($assetId);

    }

    public static function setStyleViewerAclToAsset(string $assetId) {
        $roleId=DirectoryServices::getRoleByName(UserRoles::STYLE_AND_PRODUCT_VIEWER)->role_id;
        self::setAcl($assetId, $roleId);

    }

    public static function setMarketingViewerAclToAsset(string $assetId) {
        $roleId=DirectoryServices::getRoleByName(UserRoles::MARKETING_VIEWER)->role_id;
        self::setAcl($assetId, $roleId);

    }

    private static function setAcl($assetId, $roleId, $update = 'false') {
        $acl= (object) array (
            'canUpdate'=> $update,
            'propagate'=>'false',
            'subjectId'=>$roleId,
            'subjectType'=>'role'
        );

        AssetServices::addAssetAcl($assetId, [$acl]);
    }

    private static function removeAcl($assetId, $roleId) {

        AssetServices::revokeAssetAccessToRole($assetId, $roleId);
    }


}
