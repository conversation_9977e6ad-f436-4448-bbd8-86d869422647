<?php

namespace Dam\Profile\Services;

use Dam\Core\Services\AssetLinkServicesV2;
use Dam\Core\Services\Log;
use Dam\Profile\OvsConstants;
use Dam\Profile\RequestStatus;
use Dam\Core\Services\MetadataServices;
use Dam\Profile\StoreStatus;
use Dam\Profile\Visual\TaskStatus;
use MailServices;
use Shell;
use Dam\Profile\Visual\Utils\VisualUtils;
use QueryBuilder;

class VisualWorkflowActiveServices {

    /**
     * Questo metodo viene invocato periodicamente.
     * Recupera tutte le VmRequest che sono state inviate dal Visual Retail e crea i relativi task.
     * Quest'operazione viene fatta in background perche' puo' richiedere molto tempo
     * (es. 250 negozi da elabora)
     */
    public static function sendRequestsFromQueue() {
        $timeLimit = \Shell::getConfig('vmRequestSender.scriptTimeLimit', 0);
        set_time_limit($timeLimit);  // NOTA: questo time limit non include query e tempo speso lato chiamate REST a Java, vedere documentazione

        $qb = new \QueryBuilder();
        $qb->table('common_metadata')
        ->columns('common_metadata.asset_id, common_metadata.state, assets.date_created')
        ->leftJoin('assets', 'assets.asset_id = common_metadata.asset_id')
        ->where('asset_subtype=?', OvsConstants::TYPE_VMREQUEST_NAME)
        ->where('common_metadata.state=?', RequestStatus::SENDING_VALUE)
        ->setMaxResults(\Shell::getConfig('ovs_request_sending_chunk_size', 10))
        ->orderBy('date_created', 'asc');
        $results = $qb->getResults();

        if($results != null && count($results) > 0) {

            foreach ($results as $res) {
                $requestAssetId = $res->asset_id;
                // TODO mi sembra manca gestione d'errore. Un errore su una request blocca tutte le altre
                self::sendRequest($requestAssetId);
            }
        }
    }

    /**
     * Usato da sendRequestsFromQueue(), prende una singola VmRequest inviata e genera i relativi task quanti sono gli store,
     * nonche' le relative mail di notifica.
     * Nota: una singola richiesta puo' generare fino a 250-300 task.
     * Pertanto, questo metodo processa un chunk di store alla volta, e solo quando non ci sono piu' store associati alla
     * request viene messa la request in OPEN.
     *
     * Per cambiare la dimensione del chunk, $config['ovs_task_for_request_chunk_size']. Default is 10.
     *
     * @param String $productAssetId
     */
    protected static function sendRequest($requestAssetId) {
        $chunkSize = \Shell::getConfig('ovs_task_for_request_chunk_size', 10);
        $chunkOfRequestStoreIds = self::getChunkOfLinkedStores($requestAssetId, $chunkSize);
        $requestAssetMetadata = MetadataServices::getAssetMetadata($requestAssetId);

        $storeCountsByUser = self::getStoreCountsByUser($requestAssetId);

        Log::getLogger()->info('VisualWorkflowActiveServices', ['Started Task sending for Request: '.$requestAssetId]);

        $notificationId = self::getOrCreateNotificationForRequest($requestAssetId, $requestAssetMetadata);

        foreach ($chunkOfRequestStoreIds as $currentAssetStoreId) {

            Log::getLogger()->info('VisualWorkflowActiveServices', ['Processing store with id: '.$currentAssetStoreId.' for Request: '.$requestAssetId]);
            $taskAssetId = self::createTaskForStore($requestAssetId, $requestAssetMetadata, $currentAssetStoreId);
            self::flagStoreAsProcessed($requestAssetId, $currentAssetStoreId);

            AssetLinkServicesV2::createAssetLink($notificationId, $taskAssetId, ["extra_data" => OvsConstants::VM_NOTIFICATION_LINK_TYPE_EXTRA_DATA]);
            self::linkNotificationToStoreUsers($notificationId, $currentAssetStoreId, $storeCountsByUser);

            // Invalida cache per il conteggio dei task
            VisualTaskCountServices::invalidateStoreManagerTaskCountByStoreAssetId($currentAssetStoreId);

            // Invia mail di notifica
            $message = 'Il Visual Retail ha richiesto il caricamento di foto per "'.$requestAssetMetadata->title.'"';
            $subject = 'Nuovo task assegnato';
            self::sendMail($currentAssetStoreId, $taskAssetId, $subject, $message);
        }

        if (count($chunkOfRequestStoreIds) == 0 || count($chunkOfRequestStoreIds) < $chunkSize) {
            self::completeRequestSending($requestAssetId, $requestAssetMetadata);
            Log::getLogger()->info('VisualWorkflowActiveServices', ['Completed Task sending for Request: '.$requestAssetId]);
        }
    }

    /**
     * Ritorna un chunk di store IDs aperti associati alla request tra quelli con asset_links.extra_flag=false.
     * Man mano che verranno processati, verra' l'extra_flag viene messo a true, in modo da distinguere store
     * per cui abbiamo gia' generato i task.
     * Per cambiare la dimensione del chunk, $config['ovs_task_for_request_chunk_size']. Default is 10.
     *
     * @param $requestAssetId
     * @return array
     */
    private static function getChunkOfLinkedStores($requestAssetId, $maxResults) {
        $qb = new \QueryBuilder();
        $qb->table('asset_links')
        ->columns('asset_links.src_asset_id')
        ->join('assets', 'asset_links.src_asset_id=assets.asset_id')
        ->join('common_metadata', 'common_metadata.asset_id = asset_links.src_asset_id')
        ->where('assets.asset_subtype=?', OvsConstants::TYPE_STORE_NAME)
        ->where('asset_links.dst_asset_id=?', $requestAssetId)
        ->where('asset_links.extra_flag=false')
        ->where('common_metadata.state != ?', StoreStatus::CLOSED_VALUE)
        ->setMaxResults($maxResults);

        $results = $qb->getResults();
        $toReturn = [];

        foreach ($results as $row) {
            array_push($toReturn, $row->src_asset_id);
        }
        return $toReturn;
    }

    private static function createTaskForStore ($requestAssetId, $requestAssetMetadata, $storeId) {

        //CREO ASSET TASK
        $taskAssetToCreate = new \stdClass();
        $taskAssetToCreate->assetTypeId= \AssetTypes::BUSINESS_ENTITY_ID;
        $taskAssetToCreate->assetSubtype=OvsConstants::TYPE_VMTASK_NAME;

        //La creazione del task deve essere asincrona
        $options = [
            'asynch_reindex' => true
        ];

        $taskAssetId = \AssetServices::createAsset($taskAssetToCreate, $options);

        //VALORIZZO I METADATI
        $metadataTask = new \stdClass();
        $metadataTask->store_id = $storeId;
        $metadataTask->request_id = $requestAssetId;
        $metadataTask->state_task = TaskStatus::OPEN;
        $metadataTask->evaluator =  isset($requestAssetMetadata->evaluator) ? $requestAssetMetadata->evaluator : null;
        $metadataTask->reopened = false;
        $storeCodeForTitle = VisualUtils::getStoreCodeByAssetId($storeId);

        if (isset($storeCodeForTitle)) {

            //Store code come titolo task
            $metadataTask->title= $storeCodeForTitle.'-'.$requestAssetMetadata->title;
        } else {

            //Fallback in caso di mancanza di store_code
            $metadataTask->title= $requestAssetMetadata->title;
        }

        //SETTO ACL
        OvsAclServices::setVisualMerchandiseAclToAsset($taskAssetId);
        OvsAclServices::setVisualAreaAclToAsset($taskAssetId);
        OvsAclServices::setStoreAssistantAclToAsset($taskAssetId);

        //Save task metadata
        MetadataServices::saveAssetMetadata($taskAssetId,$metadataTask);
        return $taskAssetId;

    }

    private static function getStoreCountsByUser($requestAssetId) {
        $qb = new \QueryBuilder();
        $qb->table('asset_links al')
            ->columns('cm.zone_visual_id, cm.delegated_visual_id, COUNT(al.src_asset_id) as store_count')
            ->join('common_metadata cm', 'al.src_asset_id = cm.asset_id')
            ->where('al.dst_asset_id = ?', [$requestAssetId])
            ->where('cm.zone_visual_id IS NOT NULL OR cm.delegated_visual_id IS NOT NULL')
            ->groupBy('cm.zone_visual_id, cm.delegated_visual_id');

        $results = $qb->getResults();
        $storeCounts = [];

        foreach ($results as $row) {
            $storeCount = $row->store_count;
            if (!empty($row->zone_visual_id)) {
                $storeCounts[$row->zone_visual_id] = ($storeCounts[$row->zone_visual_id] ?? 0) + $storeCount;
            }
            if (!empty($row->delegated_visual_id)) {
                $storeCounts[$row->delegated_visual_id] = ($storeCounts[$row->delegated_visual_id] ?? 0) + $storeCount;
            }
        }
        return $storeCounts;
    }

    private static function getOrCreateNotificationForRequest ($requestAssetId, $requestAssetMetadata) {
        // solo le notifiche di tipo "new task" hanno in common_metadata request_id valorizzato, sono una per richiesta
        $qb = (new QueryBuilder())
            ->columns('cm.asset_id')
            ->table('common_metadata cm')
            ->join('assets a', 'a.asset_id = cm.asset_id')
            ->where('cm.request_id = ? AND a.asset_subtype = ?', [$requestAssetId, OvsConstants::TYPE_VMNOTIFICATION_NAME]);
        $result = $qb->getResults();

        if (count($result) > 0) {
            return $result[0]->asset_id;
        }

        //CREO ASSET NOTIFICATION
        $notificationAssetToCreate = new \stdClass();
        $notificationAssetToCreate->assetTypeId= \AssetTypes::BUSINESS_ENTITY_ID;
        $notificationAssetToCreate->assetSubtype = OvsConstants::TYPE_VMNOTIFICATION_NAME;

        $notificationAssetId = \AssetServices::createAsset($notificationAssetToCreate);

        $metadataNotification = new \stdClass();
        $metadataNotification->notification_type = OvsConstants::VM_NOTIFICATION_NEW_TASK;
        $metadataNotification->title = $requestAssetMetadata->title;
        $metadataNotification->date_to_request = $requestAssetMetadata->date_to_request;
        $metadataNotification->request_id = $requestAssetId;

        MetadataServices::saveAssetMetadata($notificationAssetId, $metadataNotification);
        return $notificationAssetId;
    }

    private static function linkNotificationToStoreUsers($notificationId, $storeId, $storeCountsByUser) {

        $now = \Localization::getSystemDateTimeNow();
        $record = (new \PdoDao('common_metadata'))->getOneByFilter(
            'asset_id = ?',[$storeId],'zone_visual_id, delegated_visual_id'
        );
        if (!$record) {
            return;
        }

        $pdo = getPdo();
        $data = [];

        if (!empty($record->zone_visual_id)) {
            $data[] = [$record->zone_visual_id, $notificationId, $now, $storeCountsByUser[$record->zone_visual_id]];
        }
        if (!empty($record->delegated_visual_id)) {
            $data[] = [$record->delegated_visual_id, $notificationId, $now, $storeCountsByUser[$record->delegated_visual_id]];
        }

        $stmt = $pdo->prepare(
            "INSERT INTO users_and_notifications (user_id, notification_id, last_date_updated, num_stores) VALUES (?, ?, ?, ?) ON CONFLICT (user_id, notification_id) DO NOTHING;"
        );

        try {
            $pdo->beginTransaction();
            foreach($data as $row){
                $stmt->execute($row);
            }
            $pdo->commit();
        } catch (\Exception $e){
            $pdo->rollback();
            Log::getLogger()->info('Error linking notification to users: ' . $e->getMessage());
            throw $e;
        }
    }

    private static function completeRequestSending ($requestAssetId, $requestAssetMetadata) {
       $requestAssetMetadata->status_request=RequestStatus::OPENED_VALUE;
       MetadataServices::saveAssetMetadata($requestAssetId, $requestAssetMetadata);
    }

    /**
     * Mette a true asset_links.extra_flag in modo che sappiamo che questo store e' stato elaborato
     * per questa request.
     *
     * @param $requestAssetId
     * @param $storeId
     */
    private static function flagStoreAsProcessed($requestAssetId, $storeId) {
        $dao = new \PdoDao("asset_links");
        $dao->update(['dst_asset_id' => $requestAssetId,
                      'src_asset_id' => $storeId
        ],[
                      'extra_flag' => true
        ]);
    }

    public static function sendMail($currentStoreId, $taskAssetId, $subject, $message) {
        // Invia mail di notifica
        $storeMetadata = MetadataServices::getAssetMetadata($currentStoreId);
        $debugMode        = Shell::getConfig('debug_mode');
        $userIdsToNotify = array();
        $directorUserId = property_exists($storeMetadata, 'director') && $storeMetadata->director != null ? $storeMetadata->director : null;

        if($directorUserId != null) {
            $directorUser = \DirectoryServices::getUserById($directorUserId);
            $directorUserEmail = @$directorUser->email;

            if(!$debugMode) {
                if ($directorUserEmail != null && $directorUserEmail != ""){
                    array_push($userIdsToNotify, $directorUserId);
                }
            }
        }

        /* Delegated User rimosso
        $delegatedUserId = property_exists($storeMetadata, 'delegated_uploader') && $storeMetadata->delegated_uploader != null ? $storeMetadata->delegated_uploader : null;

        if($delegatedUserId != null) {
            $delegatedUser = \DirectoryServices::getUserById($delegatedUserId);
            $delegatedUserEmail = @$delegatedUser->email;

            if(!$debugMode)
                if ($delegatedUserEmail != null && $delegatedUserEmail != "") {
                    array_push($userIdsToNotify, $delegatedUserId);
                }
            }
        }
        */

        if(count($userIdsToNotify) > 0) {
            $mailDto = new \MailDto();
            $mailDto->subject = null; // fix per far prendere il template .subject con oggetto mail
            $mailDto->toUserIds = $userIdsToNotify;
            $mailDto->template = 'templates/velocity/visual-task-notification';
            $mailDto->assetId = $taskAssetId;
            $vmAppUrl = "/ext/vm/home";
            $mailDto->macros = [
                'vmAppUrl' => $vmAppUrl,
                'taskAssetId' => $taskAssetId,
                'subject' => $subject,
                'message' => $message
            ];
            MailServices::sendMail($mailDto);
        }
    }
}
