<?php
namespace Dam\Profile;

class OvsConstants {

    const TAXONOMY_PHOTO_SHOT_TYPE = 'Photo_Shot_Type';
    const TAXONOMY_CATALOGUE = 'Catalogues';
    const TAXONOMY_COLOR_EDITORIAL= 'Product_Color_Editorial';
    const TAXONOMY_RETURN_STORES = 'Return_Stores';

    // Catalogues
    const OVS_CATALOGUE_EXTERNAL_ID = 'ovs';
    const UPIM_CATALOGUE_EXTERNAL_ID = 'upim';

    // Channels
    const OVS_CHANNEL_GLOBAL_ID = 'ovs';
    const UPIM_CHANNEL_GLOBAL_ID = 'upim';
    const ZALANDO_CHANNEL_GLOBAL_ID = 'zalando';
    const SCHOOL_CHANNEL_GLOBAL_ID = 'school';
    const STEFANEL_CHANNEL_GLOBAL_ID = 'stefanel';
    const GAP_CHANNEL_GLOBAL_ID = 'gap';
    const PIOMBO_CHANNEL_GLOBAL_ID = 'piombo';
    const LES_COPAINS_CHANNEL_GLOBAL_ID = 'lescopains';
    const AMAZON_CHANNEL_GLOBAL_ID = 'amazon';
    const CROFF_CHANNEL_GLOBAL_ID = 'croff';


    const OVS_CHANNEL_EXTERNAL_ID = '3737';
    const UPIM_CHANNEL_EXTERNAL_ID = '7528';
    const ZALANDO_CHANNEL_EXTERNAL_ID = '7368';
    const SCHOOL_CHANNEL_EXTERNAL_ID = '8958';
    const STEFANEL_CHANNEL_EXTERNAL_ID = '9507';
    const GAP_CHANNEL_EXTERNAL_ID = '6400';
    const PIOMBO_CHANNEL_EXTERNAL_ID = '2976';
    const LES_COPAINS_CHANNEL_EXTERNAL_ID = '3085';
    const AMAZON_CHANNEL_EXTERNAL_ID = '2833';
    const CROFF_CHANNEL_EXTERNAL_ID = '0957';

    // List of all the global channel ids - used in OvsAutomaticChannelImportServices
    const ALL_GLOBAL_CHANNEL_IDS = [
        self::OVS_CHANNEL_GLOBAL_ID,
        self::UPIM_CHANNEL_GLOBAL_ID,
        self::ZALANDO_CHANNEL_GLOBAL_ID,
        self::SCHOOL_CHANNEL_GLOBAL_ID,
        self::STEFANEL_CHANNEL_GLOBAL_ID,
        self::GAP_CHANNEL_GLOBAL_ID,
        self::PIOMBO_CHANNEL_GLOBAL_ID,
        self::LES_COPAINS_CHANNEL_GLOBAL_ID,
        self::AMAZON_CHANNEL_GLOBAL_ID,
        self::CROFF_CHANNEL_GLOBAL_ID
    ];


    // const CACHE_CHANNEL_NAME = 'Cache';    deprecated
    const CACHE_V2_CHANNEL_NAME = 'Cache V2';
    const CLOUD_MEDIA_CHANNEL_NAME = 'CloudMedia';
    const QRCODE_CHANNEL_GLOBAL_ID = 'qrcode';

	const CHANNEL_TYPE_PCM = 1006;
	const CHANNEL_TYPE_CACHE = 1007;
    const CHANNEL_TYPE_CACHE_V2 = 1009;
    const CHANNEL_TYPE_CLOUD_MEDIA = 1011;
    const CHANNEL_TYPE_QR = 1010;
	const PCM_CHANNEL_ID_OVS = 3737;
	const PCM_CHANNEL_ID_UPIM = 3838;

	const TYPE_PRODUCT = 5900;
	const TYPE_PRODUCT_NAME	 = 'Product';
	const TYPE_VALIDATION_RULE_NAME	 = 'ValidationRule';
	const TYPE_VMREQUEST_NAME	 = 'VmRequest';
	const TYPE_VMTASK_NAME	 = 'VmTask';
    const TYPE_VMNOTIFICATION_NAME = 'VmNotification';
	const TYPE_STORE_NAME	 = 'Store';
    const TYPE_PERIMETER_NAME = 'Perimeter';
	const TYPE_DISPLAY_REQUEST = "Request";
	const TYPE_DISPLAY_TASK = "Task";
	const TYPE_STYLE_MODEL = "StyleModel";
	const TYPE_LOOKBOOK = "LookBook";
	const TYPE_GALLERY = "Gallery";
    const TYPE_FOLDER = "Folder";
    const TYPE_REDIRECT = "Redirect";
    const TYPE_SAMPLE_NAME	 = 'Sample';
    const TYPE_PARCEL_NAME	 = 'Parcel';
    const TYPE_TRACKED_PARCEL_NAME	 = 'TrackedParcel';

	const VM_NOTIFICATION_NEW_TASK = 2;
    const VM_NOTIFICATION_REOPENED_TASK = 3;
    const VM_NOTIFICATION_LINK_TYPE_EXTRA_DATA = "vm_notification_task";

	const WS_NAME_GALLERY = "visual-assets";

	const IMAGE_ECOMMERCE_SUBTYPE = "EcommerceImage";
	const IMAGE_COLOR_SUBTYPE = "ColorImage";
	const IMAGE_VISUAL_SUBTYPE = "VisualImage";
	const IMAGE_STYLE_SUBTYPE = "StyleImage";
    const IMAGE_STYLE_AI_SUBTYPE = "StyleImageAI";
	const IMAGE_MARKETING_SUBTYPE = "MarketingImage";
    const IMAGE_GENERIC_SUBTYPE = "GenericImage";

    const IMAGE_SUBTYPES = [self::IMAGE_ECOMMERCE_SUBTYPE, self::IMAGE_VISUAL_SUBTYPE, self::IMAGE_STYLE_SUBTYPE, self::IMAGE_STYLE_AI_SUBTYPE, self::IMAGE_MARKETING_SUBTYPE, self::IMAGE_GENERIC_SUBTYPE, self::IMAGE_COLOR_SUBTYPE];

	const DIGITAL_APPROVED_STATUS = 1;
	const DIGITAL_APPROVED_STATUS_NAME = "Approved";
	const DIGITAL_REJECTED_STATUS = 2;
	const DIGITAL_REJECTED_STATUS_NAME = "Rejected";
	const DIGITAL_TO_BE_APPROVED_STATUS = 3;
	const DIGITAL_TO_BE_APPROVED_STATUS_NAME = "To Be Approved";

	const LINKED_TO_PRODUCTS_METADATA_NO = 1;
	const LINKED_TO_PRODUCTS_METADATA_YES = 2;

	const OFFSET_OVS = 100;
    const OFFSET_SCHOOL = 200;
	const OFFSET_ZALANDO = 300;
	const OFFSET_UPIM = 400;
    const OFFSET_STEFANEL = 500;
    const OFFSET_GAP = 600;
    const OFFSET_PIOMBO = 700;
    const OFFSET_LES_COPAINS = 800;
    const OFFSET_AMAZON = 900;
    const OFFSET_CROFF = 1000;


	const AR_OVS_SCHOOL = 0.72;
	const PUBLISHING_DATA_INVALIDATING  = "INVALIDATING";
	const PUBLISHING_DATA_INVALIDATED  = "INVALIDATED";

    //Alert Types
    const SHIPPING_IN_TIME = "spedizione in tempo";
    const SHIPPING_ON_DELAY = "spedizione in ritardo";
    const DEPOSIT_IN_TIME = "magazzino in tempo";
    const DEPOSIT_ON_DELAY = "magazzino in ritardo";
    const SHOOTING_IN_TIME = "shooting in tempo";
    const SHOOTING_ON_DELAY = "shooting in ritardo";
    const PUBLISH_IN_TIME = "pubblicato in tempo";
    const PUBLISH_ON_DELAY = "pubblicato in ritardo";
    const BLOCKED = "bloccato in dogana";
    const EDITING_ON_DELAY = "foto da ritoccare";

    const EDITING_TASK_TYPE = "AdobePhotoshopTask";
    const TASKS_TABLE = 'user_tasks';

    const INGRESSO_MERCI_SAVED = "saved";
    const INGRESSO_MERCI_DRAFT = "draft";
    const INGRESSO_MERCI_TYPE_TRACKING = "tracking";
    const INGRESSO_MERCI_TYPE_LIBERO = "libero";
    const INGRESSO_MERCI_TYPE_DEPOSITO = "warehouse-request";

    // Excel import constants
	const SIZE_HEIGHT_IMPORT = "size_height";
	const CURR_SEASON_PRODUCTS_IMPORT = "curr_season_products";
    const PERIMETER_STORES_IMPORT = "perimeter_stores";

    // style model links
    const STYLE_MODEL_LINK = "style_model";
    const SAMPLE_LINK = "product_sample";
    const PARCEL_LINK = "parcel_sample";
    const PERIMETER_LINK = "store_perimeter";

    const SAMPLE_STATUS_IN_TRANSITO = 0;
    const SAMPLE_STATUS_CONFERMATO = 1;
    const SAMPLE_STATUS_ACCETTABILE = 2;
    const SAMPLE_STATUS_NON_CONFERMATO = 3;
    const SAMPLE_STATUS_NON_ACCETTABILE = 4;
    const SAMPLE_STATUS_RICHIESTO_A_DEPOSITO = 5;
    const SAMPLE_STATUS_RICHIESTO_A_NEGOZIO = 6;


    const SAMPLE_IN_TRANSITO = "In Transito";
    const SAMPLE_CONFERMATO = "Confermato";
    const SAMPLE_ACCETTABILE = "Accettabile";
    const SAMPLE_NON_CONFERMATO = "Non confermato";
    const SAMPLE_NON_ACCETTABILE = "Non accettabile";
    const SAMPLE_RICHIESTO_A_DEPOSITO = "Richiesto a deposito";
    const SAMPLE_RICHIESTO_A_NEGOZIO = "Richiesto a negozio";

    const SAMPLE_ORIGIN_OBS = "OBS";
    const SAMPLE_ORIGIN_DEPOSITO = "Deposito";
    const SAMPLE_ORIGIN_NEGOZIO = "Negozio";
    const SAMPLE_ORIGIN_FORNITORE = "Fornitore";
    const SAMPLE_ORIGIN_SEDE = "Sede";

    const RETURN_REASON_NEGOZIO = "ritorno_a_negozio";
    const RETURN_REASON_SEDE = "ritorno_a_sede";
    const RETURN_REASON_SHOWROOM = "ritorno_a_showroom";
    const RETURN_REASON_FORNITORE = "ritorno_a_fornitore";
    const RETURN_REASON_DISTRUZIONE = "distruzione";

    const SAMPLE_STATUS = [
        self::SAMPLE_STATUS_IN_TRANSITO => self::SAMPLE_IN_TRANSITO,
        self::SAMPLE_STATUS_CONFERMATO => self::SAMPLE_CONFERMATO,
        self::SAMPLE_STATUS_ACCETTABILE => self::SAMPLE_ACCETTABILE,
        self::SAMPLE_STATUS_NON_CONFERMATO => self::SAMPLE_NON_CONFERMATO,
        self::SAMPLE_STATUS_NON_ACCETTABILE => self::SAMPLE_NON_ACCETTABILE,
        self::SAMPLE_STATUS_RICHIESTO_A_DEPOSITO => self::SAMPLE_RICHIESTO_A_DEPOSITO,
        self::SAMPLE_STATUS_RICHIESTO_A_NEGOZIO => self::SAMPLE_RICHIESTO_A_NEGOZIO
    ];

    const WAREHOUSE_REQUEST_CODE_STOCK = -1;
    const WAREHOUSE_REQUEST_CODE_INSERITA = 0;
    const WAREHOUSE_REQUEST_CODE_LAVORAZIONE = 1;
    const WAREHOUSE_REQUEST_CODE_SCARTATA = 2;
    const WAREHOUSE_REQUEST_CODE_PARZ_EVASA = 3;
    const WAREHOUSE_REQUEST_CODE_EVASA = 4;

    const WAREHOUSE_REQUEST_STATUS_STOCK = "Richiesta stock";
    const WAREHOUSE_REQUEST_STATUS_INSERITA = "Inserita";
    const WAREHOUSE_REQUEST_STATUS_LAVORAZIONE = "In lavorazione";
    const WAREHOUSE_REQUEST_STATUS_SCARTATA = "Scartata";
    const WAREHOUSE_REQUEST_STATUS_PARZ_EVASA = "Parzialmente evasa";
    const WAREHOUSE_REQUEST_STATUS_EVASA = "Evasa";

    const SAMPLE_WAREHOUSE_REQUEST_STATUS = [
        self::WAREHOUSE_REQUEST_CODE_STOCK => self::WAREHOUSE_REQUEST_STATUS_STOCK,
        self::WAREHOUSE_REQUEST_CODE_INSERITA => self::WAREHOUSE_REQUEST_STATUS_INSERITA,
        self::WAREHOUSE_REQUEST_CODE_LAVORAZIONE => self::WAREHOUSE_REQUEST_STATUS_LAVORAZIONE,
        self::WAREHOUSE_REQUEST_CODE_SCARTATA => self::WAREHOUSE_REQUEST_STATUS_SCARTATA,
        self::WAREHOUSE_REQUEST_CODE_PARZ_EVASA => self::WAREHOUSE_REQUEST_STATUS_PARZ_EVASA,
        self::WAREHOUSE_REQUEST_CODE_EVASA => self::WAREHOUSE_REQUEST_STATUS_EVASA
    ];

    // parcel states
    const PARCEL_STATUS_OPEN = 'open';
    const PARCEL_STATUS_CLOSED = 'closed';


    //Perimeter Actions
    const ADD_STORES = 0;
    const REPLACE_STORES = 1;
    const DELETE_STORES = 2;

    // Types for AutomaticChannelImportServices
    const AUTOMATIC_IMPORT_TYPE = 'AUTOMATIC_IMPORT';
    const POSTPRODUCTION_TYPE = 'POSTPRODUCTION';


}

// REVIEW mettere in file separati tutte le classi in questo file
abstract class LinkingStateErrorCodes {

    const NOT_IN_STAGING = -1;
    const NOT_IN_STAGING_LABEL = "Not in Staging";
    const NOT_EXISTING_PRODUCT_CODE = 1;
    const NOT_EXISTING_PRODUCT_LABEL = "Not existing Product";
    const MISSING_EMBEDDED_PARAMETERS_CODE = 2;
    const MISSING_EMBEDDED_PARAMETERS_LABEL = "Missing IPTC SubjectCode/Keywords";
    const PUBLISHED_PRODUCT_CODE = 3;
    const PUBLISHED_PRODUCT_LABEL = "Product Already Published";
    const WRONG_SHOT_TYPE_CODE = 4;
    const WRONG_SHOT_TYPE_LABEL = "Shot type not allowed for Catalogue";
}

abstract class LoginErrorCodes {

    const NOT_ENABLED = 20;
}

abstract class ImageDomains {

    const ECOMMERCE = 1;
    const VISUAL = 2;
    const STYLE_AND_PRODUCT = 3;
    const MARKETING = 4;
    const GENERIC = 5;
    const COLOR = 6;
}

abstract class WorkspaceNames {

    const STAGING_AREA = "staging-area";
    const STYLE_AND_PRODUCT = "style-and-product-assets";
    const VISUAL = "visual-assets";

}

abstract class UserRoles {

	const ADMINISTRATOR = "Administrator";

	//Ecommerce viewer
    const ECOMMERCE_VIEWER = "Ecommerce_Viewer";
    //OLD roles
    // const ECOMMERCE_OVS_VIEWER = "Ecommerce_OVS_Viewer";
    // const ECOMMERCE_UPIM_VIEWER = "Ecommerce_UPIM_Viewer";

    const ECOMMERCE_OVS_VIEWER = 'Ecommerce_Viewer_OVS';
    const ECOMMERCE_UPIM_VIEWER = 'Ecommerce_Viewer_UPIM';
    const ECOMMERCE_ZALANDO_VIEWER = 'Ecommerce_Viewer_ZALANDO';
    const ECOMMERCE_SCHOOL_VIEWER = 'Ecommerce_Viewer_SCHOOL';
    const ECOMMERCE_GAP_VIEWER = 'Ecommerce_Viewer_GAP';
    const ECOMMERCE_PIOMBO_VIEWER = 'Ecommerce_Viewer_PIOMBO';
    const ECOMMERCE_LES_COPAINS_VIEWER = 'Ecommerce_Viewer_LES_COPAINS';
    const ECOMMERCE_AMAZON_VIEWER = 'Ecommerce_Viewer_AMAZON';
    const ECOMMERCE_CROFF_VIEWER = 'Ecommerce_Viewer_CROFF';
    const ECOMMERCE_STEFANEL_VIEWER = 'Ecommerce_Viewer_STEFANEL';



    const ECOMMERCE_EDITOR = "Ecommerce_Editor";
    const ECOMMERCE_OVS_EDITOR = "Ecommerce_OVS_Editor";
    const ECOMMERCE_UPIM_EDITOR = "Ecommerce_UPIM_Editor";


    //visual
    const VISUAL_VIEWER = "Visual_Viewer";
    const VISUAL_EDITOR = "Visual_Editor";
    const VISUAL_RETAIL= "Visual_Retail";
    const STORE_MANAGER= "Store_Manager";
    const STORE_ASSISTANT= "Store_Assistant";
    const AFFILIATE_STORE_MANAGER= "Affiliate_Store_Manager";
    const ZONE_VISUAL= "Zone_Visual";

    // Style & Product
    const STYLE_AND_PRODUCT_VIEWER = "Style_And_Product_Viewer";

    // Marketing
    const MARKETING_VIEWER = "Marketing_Viewer";

    //Redirect
    const REDIRECT_EDITOR = "Redirect_Editor";

    // Admin
    const USER_OVS_ADMIN = "Ovs_Admin";

    // Sample & Warehouse
    const INGRESSO_MERCI = "Ingresso_Merci";
    const MAGAZZINO_OVS = "Magazzino_OVS";
    const MAGAZZINO_UPIM = "Magazzino_UPIM";

}

abstract class RequestStatus {

    const DRAFT_VALUE = 1;
    const DRAFT_LABEL = "Draft";
    const SENDING_VALUE = 2;
    const SENDING_LABEL = "Sending";
    const OPENED_VALUE = 3;
    const OPENED_LABEL = "Open";
    const CLOSED_VALUE = 4;
    const CLOSED_LABEL = "Closed";


    public static function getLabelFromValue($value) {
        $label = "";

        switch($value) {
            case self::DRAFT_VALUE:
                $label = self::DRAFT_LABEL;
                break;
            case self::SENDING_VALUE:
                $label = self::SENDING_LABEL;
                break;
            case self::OPENED_VALUE:
                $label = self::OPENED_LABEL;
                break;
            case self::CLOSED_VALUE:
                $label = self::CLOSED_LABEL;
                break;
        }
        return $label;
    }

    public static function getValueFromLabel($label) {
        $value = null;

        switch($label) {
            case self::DRAFT_LABEL:
                $value = self::DRAFT_VALUE;
                break;
            case self::SENDING_LABEL:
                $value = self::SENDING_VALUE;
                break;
            case self::OPENED_LABEL:
                $value = self::OPENED_VALUE;
                break;
            case self::CLOSED_LABEL:
                $value = self::CLOSED_VALUE;
                break;
        }
        return $value;
    }


}

abstract class StateTask{

	const WAITING_UPLOAD_VALUE = 1;
	const WAITING_UPLOAD_LABEL = "Waiting Upload";
	const TO_BE_APPROVED_VALUE = 2;
	const TO_BE_APPROVED_LABEL = "To Be Approved";
	const CLOSED_VALUE = 3;
	const CLOSED_LABEL = "Closed";
    const TO_BE_RESHOOT_VALUE = 4;
    const TO_BE_RESHOOT_LABEL = "To Be Reshoot";

	public static function getLabelFromValue($value){
		$label = "";

		switch($value) {
			case self::WAITING_UPLOAD_VALUE:
				$label = self::WAITING_UPLOAD_LABEL;
				break;
			case self::TO_BE_APPROVED_VALUE:
				$label = self::TO_BE_APPROVED_LABEL;
				break;
			case self::CLOSED_VALUE:
				$label = self::CLOSED_LABEL;
				break;
            case self::TO_BE_RESHOOT_VALUE:
                $label = self::TO_BE_RESHOOT_LABEL;
                break;
		}
		return $label;
	}

}

abstract class StoreStatus{

    const NEXT_OPENING_VALUE = 1;
    const NEXT_OPENING_LABEL = "Next Opening";
    const NEXT_OPENING_WITH_MAIL_VALUE = 2;
    const NEXT_OPENING_WITH_MAIL_LABEL = "Next Opening With Mail";
    const OPEN_VALUE = 3;
    const OPEN_LABEL = "Open";
    const NEXT_CLOSING_VALUE = 4;
    const NEXT_CLOSING_LABEL = "Next Closing";
    const CLOSED_VALUE = 5;
    const CLOSED_LABEL = "Closed";
}

abstract class OrderSum {

	public static function getResult($channel , $position) {
		$newPosition=0;
		switch($channel) {
			case OvsConstants::OVS_CHANNEL_GLOBAL_ID :
				$newPosition= $position + OvsConstants::OFFSET_OVS;
				break;
			case OvsConstants::UPIM_CHANNEL_GLOBAL_ID :
				$newPosition= $position + OvsConstants::OFFSET_UPIM;
				break;
			case OvsConstants::ZALANDO_CHANNEL_GLOBAL_ID :
				$newPosition= $position + OvsConstants::OFFSET_ZALANDO;
				break;
            case OvsConstants::SCHOOL_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_SCHOOL;
                break;
            case OvsConstants::STEFANEL_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_STEFANEL;
                break;
            case OvsConstants::GAP_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_GAP;
                break;
            case OvsConstants::PIOMBO_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_PIOMBO;
                break;
            case OvsConstants::LES_COPAINS_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_LES_COPAINS;
                break;
            case OvsConstants::AMAZON_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_AMAZON;
                break;
            case OvsConstants::CROFF_CHANNEL_GLOBAL_ID:
                $newPosition= $position + OvsConstants::OFFSET_CROFF;
                break;
			default:
				$newPosition = $position;
				break;
		}
		return $newPosition;

	}

}

abstract class ColorRequestOnCalendar {

	const ENV1= '#A49D65';
	const ENV2= '#6281BB';
	const ENV3= '#7DBA5F';
	const ENV4= '#F6C481';
	const ENV5= '#BA3953';
	const ENV6= '#9E5A5A';
	const ENV7= '#c99883';
	const ENV8= '#DC928A';
	const ENV9= '#864F6B';
	const ENV10= '#D5B875';

	public static function getColorValue($value) {
		$color = "white";

		switch($value) {
			case 'ENV1':
				$color = self::ENV1;
				break;
			case 'ENV2':
				$color = self::ENV2;
				break;
			case 'ENV3':
				$color = self::ENV3;
				break;
			case 'ENV4':
				$color = self::ENV4;
				break;
			case 'ENV5':
				$color = self::ENV5;
				break;
			case 'ENV6':
				$color = self::ENV6;
				break;
			case 'ENV7':
				$color = self::ENV7;
				break;
			case 'ENV8':
				$color = self::ENV8;
				break;
			case 'ENV9':
				$color = self::ENV9;
				break;
			case 'ENV10':
				$color = self::ENV10;
				break;
		}
		return $color;
	}
}

abstract class MediaRolesCustom {

    const ECOMMERCE_RENDITION_ROLE_ID = 100;
    const S_RENDITION_ROLE_ID = 106;
    const M_RENDITION_ROLE_ID = 107;
    const L_RENDITION_ROLE_ID = 108;
    const H476JPEG_RENDITION_ROLE_ID = 112;
    const H111JPEG_RENDITION_ROLE_ID = 113;
    const H50JPEG_RENDITION_ROLE_ID = 114;
    const RESIZED_ESSENCE_RENDITION_ROLE_ID = 115;
    const ECOMMERCE_RENDITION_ROLE_NAME = 'ecommerce';
    const S_RENDITION_ROLE_NAME = 's';
    const M_RENDITION_ROLE_NAME = 'm';
    const L_RENDITION_ROLE_NAME = 'l';
    const H476_RENDITION_ROLE_NAME = 'h476jpeg';
    const H111_RENDITION_ROLE_NAME = 'h111jpeg';
    const H50_RENDITION_ROLE_NAME = 'h50jpeg';
    const RESIZED_ESSENCE_RENDITION_ROLE_NAME = 'resized-essence';
}