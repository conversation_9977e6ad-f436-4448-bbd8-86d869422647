<?php

use Dam\Core\Services\Log;
use Dam\Core\Services\MetadataServices;
use Dam\Core\Services\TaxonomyServices;
use Dam\Ext\AdvancedProductWorkflow\Services\Constants\AdvancedProductWorkflowConstants;
use Dam\Ext\AdvancedProductWorkflow\Services\Enum\ShotStatusEnum;
use Dam\Profile\ApprovalStatusEnum;
use Dam\Profile\LinkingStateErrorCodes;
use Dam\Profile\OvsConstants;
use Dam\Profile\Services\OvsAclServices;
use Dam\Profile\Services\OvsAlertingServices;
use Dam\Profile\Services\OvsChannelServices;
use Dam\Profile\Services\OvsImportServices;
use Dam\Profile\Services\OvsProductCodeEanServices;
use Dam\Profile\Services\OvsStagingAreaServices;
use Dam\Profile\Services\OvsYearsAndPeriodsServices;
use Dam\Profile\Services\ReportStagioneServices;
use Dam\Profile\Style\Utils\StyleUtils;

use Dam\Profile\Utils\OvsUtils;

require_once getProfilePath('pages/import/ImportUtils.class.php');
require_once getProfilePath('pages/import/ProductMetadata.php');

/*
 * Questa classe si occupa del parsing degli XML provenienti dal PCM. Crea gli asset, aggiunge i metadati e prova a
 * linkare eventuali immagini presenti in Staging Area
*/

class ProductParser {

    var $fullProductXml;
    var $catalogueId;
	var $productMetadata;
	var $taxonomies;
	var $currentAttribute;
	var $language;
	var $summary = array();
	var $description = array();
 	var $variant2values = array();
 	var $variant2valuesDisplayNames = array();
 	var $eanCodes = array();
	var $eansSizeCodes = array();
 	var $eanSizes = array();
	var $channelExternalIds = array();
	var $productCounter = 0;
	var $defaultAttrLanguage = 'it_IT';
	var $catalogueExternalId;
	var $colorGroup = array();
	var $colorEditorial = array();
	var $eans = [];
	var $availableChannelIds = array();
	var $technicalPlusValues = array();
	var $technicalOptionsValues = array();
	var $isTechnicalOptions = false;
	var $currentTechOptionsValue;
	var $currentChannel;
	var $yearAndPeriodPerChannelValues = array();


	function __construct ($channelIds = []) {
		// Array di channelIds, verranno ignorati se siamo in product parser (aka import PCM), sono invece utilizzati in ProductAzureParser
	    $this->availableChannelIds = $channelIds;
	}

	function start_element($parser, $tag, $attributes) {
	    // 1. Build the full tag structure
	    if ('product' == $tag) {
	        $this->fullProductXml = ''; // start the product tag
	    }

	    $this->fullProductXml .= $this->buildStartTagAndAttributes($tag, $attributes);
	    // 2. Parse each attribute
	    // Catalogue ID
	    if ($tag == 'pcm') {
	        $channelServices = new OvsChannelServices();
	        $this->catalogueExternalId = strtolower($attributes['catalog']);
	        $this->catalogueId = $channelServices->getCatalogueIdByExternalId($this->catalogueExternalId);
	    } elseif ('product' == $tag) {
		    $this->productCounter++;
			$this->productMetadata = new ProductMetadata();
			$this->taxonomies = array();

			// erpId, productCode, masterId
			$this->productMetadata->erp_id = $attributes['id'];
			$this->productMetadata->product_code = $attributes['id'];
			$this->productMetadata->master_id = $attributes['master-id'];
			$this->productMetadata->type = $attributes['type'];
			$this->productMetadata->catalogue = $this->catalogueId;
			$this->productMetadata->is_master = $attributes['isMaster'];

			// PCM Type
		    $pcmTypeDescription = $attributes['type'];

		    if($pcmTypeDescription != null && $pcmTypeDescription != "") {
		        $taxonomy = new stdClass();
		        $taxonomy->taxName = 'PCM_Type';
		        $taxonomy->metadataField = 'pcm_type';
		        $taxonomy->termDescription = $pcmTypeDescription;
		        array_push($this->taxonomies, $taxonomy);
		    }

			Log::getLogger()->info('XML Import', ['Processing product#'.$this->productCounter. ' with productCode: '.$this->productMetadata->erp_id]);
		} elseif (!empty($this->productMetadata)) {

			switch ($tag) {
				case 'ean':
					// Salvataggio in una stringa del tipo "8050514506835 8050514506842 8050514506859"
					if (empty($this->productMetadata->ean)) {
						$this->productMetadata->ean = $attributes['ean-code'];
					} else {
						$this->productMetadata->ean = $this->productMetadata->ean . ' ' . $attributes['ean-code'];
					}
					$ean = $attributes['ean-code'];
					$this->eans[] = $ean;

					// Gli ean vengono salvati anche in un array per un possibile utilizzo futuro.
					// Attualmente nell'end_element viene creato un array che associa ean e taglie.
					$variantValue = $attributes['variant-value'];
					$this->eanCodes[strval($variantValue)] = $attributes['ean-code'];
					$this->eansSizeCodes[$ean] = $variantValue;
					break;

				case 'channel':
					if (!in_array(@$attributes['id'], $this->availableChannelIds)) {
						$this->channelExternalIds[] = $attributes['id'];
						$channelServices = new OvsChannelServices();
						$this->currentChannel = $channelServices->getChannelIdByExternalId($attributes['id']);
						$this->yearAndPeriodPerChannelValues[$this->currentChannel] = [];
					}
					break;

				case 'distributed':
					array_push($this->yearAndPeriodPerChannelValues[$this->currentChannel], $attributes['value']);
					break;

				case 'attribute':
					self::manageAttributeTag($attributes);
					break;

				case 'variant2-value':
					array_push($this->variant2values, $attributes['value']);
					$this->currentAttribute = 'variant2-value';
					break;
				case 'technicalOptions':
					$this->isTechnicalOptions = true;
					break;

				case 'display-name':
					switch ($attributes['lang']) {
						case 'it_IT':
							$this->language = 'it_IT';
							break;
						case 'de_IT':
							$this->language = 'de_DE';
							break;
						case 'es_IT':
							$this->language = 'es_ES';
							break;
						case 'sl_IT':
							$this->language = 'sl_SI';
							break;
						case 'fr_IT':
							$this->language = 'fr_FR';
							break;
						case 'en_IT':
							$this->language = 'en_US';
							break;
						default:
							$this->language = 'otherLang';
							break;
					}
			}

			// Aggiorno $this->currentAttribute
			if ($tag == 'attribute' &&
					($attributes['name'] != 'summary') &&
					($attributes['name'] != 'description') &&
					($attributes['name'] != 'colorGroup') &&
					($attributes['name'] != 'colorEditorial') &&
					($attributes['name'] != 'variant2-value') &&
					($attributes['name'] != 'technicalPlus') &&
					($attributes['name'] != 'technicalOptions')
					) {
						if($this->isTechnicalOptions) {
							$this->currentAttribute = $attributes['name'];
							if(!empty($attributes['value'])) {
								$this->currentTechOptionsValue = $attributes['value'];
							}
						} else {
							$this->currentAttribute = 'otherAttribute';
						}
			}
		}
	}


	function character_data($parser, $data) {
	    $this->fullProductXml .= html_entity_decode($data); // Build the full XML attribute

		if($this->isTechnicalOptions && $this->currentAttribute != null && $this->currentAttribute != 'otherAttribute' ){

			if( empty($this->technicalOptionsValues[$this->currentAttribute])){
				$this->technicalOptionsValues[$this->currentAttribute] = new stdClass();
			}
			if(! empty($this->language) && !empty($data) && trim($data) != "" ){
				$tmpLanguage = $this->language;
				$this->technicalOptionsValues[$this->currentAttribute]->$tmpLanguage = $this->currentAttribute . ": " . ucfirst(strtolower(trim($data)));
			} else if(! empty($this->currentTechOptionsValue) && trim($this->currentTechOptionsValue) != "") {
				$value = trim($this->currentTechOptionsValue) == "true" ? $this->currentAttribute . ": Yes" : $this->currentAttribute . ": No";
				$it = "it_IT";
				$en = "en_US";
				$this->technicalOptionsValues[$this->currentAttribute]->$it = $value;
				$this->technicalOptionsValues[$this->currentAttribute]->$en = $value;

				unset($this->currentTechOptionsValue);
			}
		} else if ( (!empty($data) || $this->currentAttribute == 'variant2-value') && trim($data) != "")  {
			switch ($this->currentAttribute) {
				case 'summary':
						if (array_key_exists($this->language, $this->summary)) {
							$this->summary[$this->language] = $this->summary[$this->language] . trim($data);
						} else {
							$this->summary[$this->language] = trim($data);
						}
					break;

				case 'technicalPlus':
						$this->technicalPlusValues[] = trim($data);
					break;

				case 'description':
						if (array_key_exists($this->language, $this->description)) {
							$this->description[$this->language] = $this->description[$this->language] . trim($data);
						} else {
							$this->description[$this->language] = trim($data);
					}
					break;

				case 'colorGroup':
						if (array_key_exists($this->language, $this->colorGroup)) {
							$this->colorGroup[$this->language] = $this->colorGroup[$this->language] . trim($data);
						} else {
							$this->colorGroup[$this->language] = trim($data);
					}
					break;

				case 'colorEditorial':
						if (array_key_exists($this->language, $this->colorEditorial)) {
							$this->colorEditorial[$this->language] = $this->colorEditorial[$this->language] . trim($data);
						} else {
							$this->colorEditorial[$this->language] = trim($data);
					}
					break;

				case 'variant2-value':
					$empty = strval($data) === "0" ? false : empty($data);
					if (!$empty) {
						array_push($this->variant2valuesDisplayNames, trim($data));
					}
					break;
			}
		}
	}

	function end_element($parser, $tag) {
		$config=Shell::getConfig();
	    $this->fullProductXml .= $this->buildEndTagAndAttributes($tag); // Build the full XML attribute

		if('technicalOptions' == $tag) {
			$this->isTechnicalOptions = false;
		} elseif ('product' == $tag) {

		    $isProductUpdated = false;
		    $isNewProduct = false;
		    $isLocked=false;
		    $notes="Product added";

			$sourceCode = @$this->productMetadata->erp_id;

			try {

			     if (!empty($sourceCode)) { // Solo se ho il source code creo il nuovo prodotto

    				$qb = new QueryBuilder();
    				$qb->columns('asset_id, asset_locked')
    				->table('common_metadata')
                        ->where('product_code=?', [
                        $this->productMetadata->product_code
                    ]);

                    $assetIds = $qb->getResults();

                    if (empty($assetIds)) {
                        $asset = new stdClass();
                        $asset->assetTypeName = 'Product';

                        //La creazione del prodotto deve essere asincrona
                        $options = [
                            'asynch_reindex' => true
                        ];

                        $assetId = AssetServices::createAsset($asset, $options);
                        $isNewProduct = true;
						Log::getLogger()->info('XML Import', ['Product# '.$this->productMetadata->erp_id . ' is new. AssetId: '.$assetId]);

    				}
    				else {
    				    $assetId = $assetIds[0]->asset_id;
    				    $isLocked = $assetIds[0]->asset_locked;
    				    $notes="Product updated";

    				    $isProductUpdated = self::_checkIsUpdated($assetId, $this->productMetadata->year);
						Log::getLogger()->info('XML Import', ['Product# '.$this->productMetadata->erp_id . ' update state: '. $isProductUpdated . ' AssetId: '.$assetId]);

    				}

                     //Set Ecommerce_view ACL to asset every time (in order to manage multi catalogue)
                    //OvsAclServices::setEcommerceViewAclToAssetByExternalId($assetId, $this->catalogueExternalId, $this->channelExternalIds);

					OvsAclServices::setChannelEcommerceViewAclToAssetByExternalId($assetId, $this->channelExternalIds);

    				if(!$isProductUpdated) {

        				$this->productMetadata->assetId = $assetId;

        				if ($isLocked != true) {

                            if (! empty($assetId)) {
                                // Log event into import_status table
                                ImportUtils::logEvent($sourceCode, "SAP Hybris Import", "SUCCESS",$notes, null, $assetId);
                            }

                            // Creare o associare la tassonomia eCommerce di livello 0 formata da anno e period (ad esempio 2018 Primavera-Estate)
                            $eCommerceDescription = null;
                            $eCommerceTermId = null;
                            $eCommerceTermIds = array();

                            // Tassonomia Product_Category (and Department)
                            $categoryTermDescr = "";
                            $categoryTermId = null;
                            $categoryTermIds = array();

                            if (! empty($this->productMetadata->year) && ! empty($this->productMetadata->period)) {
                                $eCommerceDescription = ImportUtils::getEcommerceDescription($this->productMetadata->year, $this->productMetadata->period);
                                $eCommerceTermId = ImportUtils::getOrCreateTaxonomyTerm($eCommerceDescription, 'eCommerce', 0, null);
                                array_push($eCommerceTermIds, $eCommerceTermId);
                            }

                            // Taxonomies (se non esistenti, crearle ed associarle all'asset)
                            $taxonomies = $this->taxonomies;
                            $categoryEcommerceTermId = null;

                            if ($taxonomies != null && count($taxonomies) > 0) {

                                foreach ($taxonomies as $taxonomy) {

                                    if ($taxonomy->taxName == 'Product_Category' && ! (empty($eCommerceTermId))) {
                                        /*
                                         * Creazione della tassonomia di tipo "eCommerce" di secondo livello.
                                         * A partire dall'anno, dalla stagione e dalla categoria del prodotto viene creata la tassonomia eCommerce figlia (ad es. OVS WOMAN CATEGORY (2019-1)),
                                         * che viene associata alla tassonomia padre precedentemente creata ($eCommerceTermId).
                                         */
                                        $categoryEcommerceTermDescr = $taxonomy->termDescription . ' (' . $this->productMetadata->year . '-' . $this->productMetadata->period . ')';
                                        $categoryEcommerceTermId = ImportUtils::getOrCreateTaxonomyTerm($categoryEcommerceTermDescr, 'eCommerce', 1, $eCommerceTermId);
                                        array_push($eCommerceTermIds, $categoryEcommerceTermId);
                                        // Update translation
                                        $params = new stdClass();
                                        $params->term_id = $categoryEcommerceTermId;
                                        $params->language = 'en_US';
                                        $params->description = $taxonomy->termDescription;
                                        $ecommerceTaxonomyId = TaxonomyServices::getTaxonomyByName('eCommerce')->id;
                                        $params->taxonomy_id = $ecommerceTaxonomyId;
                                        TaxonomyServices::updateTaxonomyTerm($params);

                                        /*
                                         * Creazione della tassonomia di tipo "Product_Category" di primo livello.
                                         */
                                        $categoryTermDescr = $taxonomy->termDescription;
                                        $categoryTermId = ImportUtils::getOrCreateTaxonomyTerm($categoryTermDescr, 'Product_Category', 0, null);
                                        array_push($categoryTermIds, $categoryTermId);
                                        // Update translation
                                        $params = new stdClass();
                                        $params->term_id = $categoryTermId;
                                        $params->language = 'en_US';
                                        $params->description = $taxonomy->termDescription;
                                        $categoryTaxonomyId = TaxonomyServices::getTaxonomyByName('Product_Category')->id;
                                        $params->taxonomy_id = $categoryTaxonomyId;
                                        TaxonomyServices::updateTaxonomyTerm($params);
                                    }

                                    // TODO: nel caso in cui la tassonomia Department venisse letta prima della tassonomia Category, non si entrerebbe mai nel seguente if. Da migliorare.
                                    if ($taxonomy->taxName == 'Product_Department' && ! (empty($categoryEcommerceTermId))) {
                                        /*
                                         * Creazione della tassonomia di tipo "eCommerce" di terzo livello.
                                         * A partire dall'anno, dalla stagione, dalla categoria e dal dipartimento del prodotto viene creata la tassonomia eCommerce figlia di terzo livello
                                         * (ad es. OUTERWEAR YOUNG (OVS WOMAN CATEGORY (2019-1))),
                                         * che viene associata alla tassonomia padre (di primo livello) precedentemente creata ($categoryEcommerceTermId).
                                         */
                                        $departmentEcommerceTermDescr = $taxonomy->termDescription . ' (' . $categoryEcommerceTermDescr . ')';
                                        $departmentEcommerceTermId = ImportUtils::getOrCreateTaxonomyTerm($departmentEcommerceTermDescr, 'eCommerce', 2, $categoryEcommerceTermId);
                                        array_push($eCommerceTermIds, $departmentEcommerceTermId);
                                        // Update translation
                                        $params = new stdClass();
                                        $params->term_id = $departmentEcommerceTermId;
                                        $params->language = 'en_US';
                                        $params->description = $taxonomy->termDescription;
                                        $ecommerceTaxonomyId = TaxonomyServices::getTaxonomyByName('eCommerce')->id;
                                        $params->taxonomy_id = $ecommerceTaxonomyId;
                                        TaxonomyServices::updateTaxonomyTerm($params);

                                        /*
                                         * Creazione della tassonomia di tipo "Product_Category" di secondo livello.
                                         * A partire dalla categoria e dal dipartimento del prodotto viene creata la tassonomia Product_Category figlia di secondo livello
                                         * (ad es. OUTERWEAR YOUNG (OVS WOMAN CATEGORY)),
                                         * che viene associata alla tassonomia padre (di primo livello) precedentemente creata ($categoryTermId).
                                         */
                                        $departmentTermDescr = $taxonomy->termDescription;
                                        $departmentTermId = ImportUtils::getOrCreateTaxonomyTerm($departmentTermDescr, 'Product_Category', 1, $categoryTermId);
                                        array_push($categoryTermIds, $departmentTermId);
                                        // Update translation
                                        $params = new stdClass();
                                        $params->term_id = $departmentTermId;
                                        $params->language = 'en_US';
                                        $params->description = $taxonomy->termDescription;
                                        $ecommerceTaxonomyId = TaxonomyServices::getTaxonomyByName('Product_Category')->id;
                                        $params->taxonomy_id = $ecommerceTaxonomyId;
                                        TaxonomyServices::updateTaxonomyTerm($params);
                                    }

                                    $taxTermId = ImportUtils::getOrCreateTaxonomyTerm($taxonomy->termDescription, $taxonomy->taxName, 0, null);

                                    if ($taxTermId != null) {

                                        switch ($taxonomy->taxName) {
                                            case 'Product_Brand':
                                                $this->productMetadata->brand = [
                                                    $taxTermId
                                                ];
                                                break;
                                            // case 'Product_Category':
                                            // $this->productMetadata->categories = [$taxTermId];
                                            // break;
                                            case 'Product_Function':
                                                $this->productMetadata->function = [
                                                    $taxTermId
                                                ];
                                                break;
                                            case 'Product_Color':
                                                $this->productMetadata->color = [
                                                    $taxTermId
                                                ];
                                                break;
                                            case 'Countries':
                                                $this->productMetadata->country_of_origin = [
                                                    $taxTermId
                                                ];
                                                break;
                                            case 'Product_Season':
                                                $this->productMetadata->season = [
                                                    $taxTermId
                                                ];
                                                break;
                                            case 'Product_Gender':
                                                $this->productMetadata->gender = [
                                                    $taxTermId
                                                ];
                                                break;
                                            case 'PCM_Type':
                                                $this->productMetadata->pcm_type = [
                                                    $taxTermId
                                                ];
                                                break;
                                            // case 'Product_Department':
                                            // $this->productMetadata->department = [$taxTermId];
                                            // break;
                                        }
                                    }
                                }
                            }

                            if (! empty($eCommerceTermIds) && count($eCommerceTermIds) > 0) {
                                $this->productMetadata->ecommerce = $eCommerceTermIds;
                            }

                            if (! empty($categoryTermIds) && count($categoryTermIds) > 0) {
                                $this->productMetadata->categories = $categoryTermIds;
                            }

                            // Summary, Description
                            $translations = array();

                            if (! empty($this->summary) && count($this->summary) > 0) {

                                foreach ($this->summary as $summaryTransLang => $summaryTransValue) {
                                    $dto = new stdClass();
                                    $dto->short_description_t = $summaryTransValue;
                                    $translations[$summaryTransLang] = $dto;
                                }
                            }

                            if (! empty($this->description) && count($this->description) > 0) {

                                foreach ($this->description as $descriptionTransLang => $descriptionTransValue) {

                                    if (array_key_exists($descriptionTransLang, $translations)) {
                                        $dto = $translations[$descriptionTransLang];
                                    } else {
                                        $dto = new stdClass();
                                    }

                                    $dto->description = $descriptionTransValue;
                                    $translations[$descriptionTransLang] = $dto;
                                }
                            }

                            if (count($translations) > 0) {
                                $this->productMetadata->translations = $translations;
                            }

                            if(!empty($this->colorGroup) && count($this->colorGroup) > 0) {
                                $colorGroupItDescr = $this->colorGroup['it_IT'] ?? '';
                                if ($colorGroupItDescr === '') {
                                    Log::getLogger()->warn('XML Import: no color group info provided for IT lang');
                                }

                                $colorGroupTermId = ImportUtils::getOrCreateTaxonomyTerm($colorGroupItDescr, 'Product_Color_Group', 0, null);
                                $this->productMetadata->color_group = [$colorGroupTermId];

                                // Update translation
								if(!empty($this->colorGroup['en_US'])){
									$params = new stdClass();
									$params->term_id = $colorGroupTermId;
									$params->language = 'en_US';
									$colorGroupEnDescr = $this->colorGroup['en_US'];
									$params->description = $colorGroupEnDescr;
									$colorGroupTaxonomyId = TaxonomyServices::getTaxonomyByName('Product_Color_Group')->id;
									$params->taxonomy_id = $colorGroupTaxonomyId;
									TaxonomyServices::updateTaxonomyTerm($params);
								}
                            }

                            if(!empty($this->colorEditorial) && count($this->colorEditorial) > 0) {
                                $colorEditorialItDescr = $this->colorEditorial['it_IT'] ?? '';

                                if ($colorEditorialItDescr === '') {
                                    Log::getLogger()->warn('XML Import: no color editorial info provided for IT lang');
                                }


                                $colorEditorialTermId = ImportUtils::getOrCreateTaxonomyTerm($colorEditorialItDescr, 'Product_Color_Editorial', 0, null);
                                $this->productMetadata->color_editorial = [$colorEditorialTermId];

                                // Update translation
								if(!empty($this->colorEditorial['en_US'])){
									$params = new stdClass();
									$params->term_id = $colorEditorialTermId;
									$params->language = 'en_US';
									$colorEditorialEnDescr = $this->colorEditorial['en_US'];
									$params->description = $colorEditorialEnDescr;
									$colorEditorialTaxonomyId = TaxonomyServices::getTaxonomyByName('Product_Color_Editorial')->id;
									$params->taxonomy_id = $colorEditorialTaxonomyId;
									TaxonomyServices::updateTaxonomyTerm($params);
								}
                            }

							$eansAndSizes = [];
                             if (count($this->variant2values) > 0) {

                                $i = 0;

                                 foreach ($this->variant2values as $variant2Value) {
                                   $this->eanSizes[strval($variant2Value)] = $this->variant2valuesDisplayNames[$i];
                                   $i ++;
                                 }

                                 if (! empty($this->eanCodes)) {

                                    //$eansAndSizes = array_merge_recursive($this->eanCodes, $this->eanSizes);
									foreach($this->eanCodes as $code => $eanCode){
										$eansAndSizes["$code"] = [$this->eanCodes["$code"], $this->eanSizes["$code"]];
									}
								}

                             }

							if(isset($this->technicalPlusValues) && count($this->technicalPlusValues) > 0){
								$technicalPlusValuesIds = array();

								foreach ($this->technicalPlusValues as $technicalPlusValue) {
									$technicalPlusValuesId = ImportUtils::getOrCreateTaxonomyTerm($technicalPlusValue, 'Technical_Plus', 0, null);
									$technicalPlusValuesIds[] = $technicalPlusValuesId;
								}
								$this->productMetadata->technical_plus = $technicalPlusValuesIds;
							}

							if(isset($this->technicalOptionsValues) && count($this->technicalOptionsValues) > 0){
								$techOptionsIds = array();

								foreach ($this->technicalOptionsValues as $techOptionName => $technicalOptionsValue) {
									$rootTermId = ImportUtils::getOrCreateTaxonomyTerm($techOptionName, 'Technical_Options', 0, null);
									//$techOptionsIds[] = $rootTermId;

									if(!empty($technicalOptionsValue->it_IT)){
										$techOptsTermId = ImportUtils::getOrCreateTaxonomyTerm($technicalOptionsValue->it_IT, 'Technical_Options', 1, $rootTermId);
										$techOptionsIds[] = $techOptsTermId;
										foreach($technicalOptionsValue as $lang => $value){
											// add translations
											$params = new stdClass();
											$params->term_id = $techOptsTermId;
											$params->language = $lang;
											$params->description = $value;
											$techOptsTaxonomyId = TaxonomyServices::getTaxonomyByName('Technical_Options')->id;
											$params->taxonomy_id = $techOptsTaxonomyId;
											TaxonomyServices::updateTaxonomyTerm($params);
										}
									}
								}
								$this->productMetadata->technical_options = $techOptionsIds;
							}
							$legacyMode = Shell::getConfig("legacy_mode", true);

							//controllare se il prodotto è nuovo



							if($legacyMode && $isNewProduct) {
								OvsUtils::setProductToLegacy($assetId);
							}
							if(isset($this->channelExternalIds) && count($this->channelExternalIds) > 0) {
								// Save assets in channels for the specified channels

								foreach ($this->channelExternalIds as $channelExternalId) {
									$channelServices = new OvsChannelServices();
									$channelId = $channelServices->getChannelIdByExternalId($channelExternalId);
									$channelServices->getOrCreateAssetInChannel($assetId, $channelId); // initialize the needed asset in channel
									if(ModuleManager::isModuleAvailable('AdvancedProductWorkflow')){
										if((!$legacyMode || !OvsUtils::isLegacy($assetId))){

											$isAssetInDeadlinesTable = ReportStagioneServices::checkIfAssetIsInDeadlinesTable($assetId, $channelId);

											// controllo per stagioni passate
											$currentYearAndPeriod = ReportStagioneServices::getCurrentYearAndPeriod($channelExternalId);
											$isPastShootingSeason = $this->productMetadata->shootingYear < $currentYearAndPeriod->shooting_year || (
												$this->productMetadata->shootingYear == $currentYearAndPeriod->shooting_year &&
												$this->productMetadata->shootingPeriod < $currentYearAndPeriod->shooting_period);

											$deadlineUtils = OvsAlertingServices::makeNewWithProductAndChannelId($assetId, $channelId);

											if ($isPastShootingSeason) {

												$isAssetInReportDeadlinesTable = ReportStagioneServices::isAssetInReportDeadlinesTable($assetId, $channelId);

												if ($isAssetInDeadlinesTable) {
													// se presente nella tabella delle deadline, aggiorna
													$dateOfSell = MetadataServices::getAssetMetadata($assetId)->date_of_sell;
													if($this->productMetadata->date_of_sell != null && ($dateOfSell != $this->productMetadata->date_of_sell)){
														$deadlineUtils->updateAllDeadlines(DateTime::createFromFormat("Y-m-d", $this->productMetadata->date_of_sell));
													}
												} elseif ($isAssetInReportDeadlinesTable) {
													$latestReportSeason = ReportStagioneServices::getLatestReportSeason($assetId, $channelId);
													[$latestReportShootingYear, $latestReportShootingPeriod] = explode(',', $latestReportSeason);

													// se presente in report con stagione precedente, aggiungi in pcds
													if ($latestReportShootingYear < $this->productMetadata->shootingYear || ($latestReportShootingYear == $this->productMetadata->shootingYear &&
														$latestReportShootingPeriod < $this->productMetadata->shootingPeriod)) {

														if(!empty($this->productMetadata->date_of_sell)){
															$deadlineUtils->setProductDeadlines(DateTime::createFromFormat("Y-m-d", $this->productMetadata->date_of_sell));
														} else {
															$deadlineUtils->insertEmptyProductDeadlines();
														}
													}
												} else {
													if(!empty($this->productMetadata->date_of_sell)){
														$deadlineUtils->setProductDeadlines(DateTime::createFromFormat("Y-m-d", $this->productMetadata->date_of_sell));
													} else {
														$deadlineUtils->insertEmptyProductDeadlines();
													}
												}
											} elseif ($isNewProduct || !$isAssetInDeadlinesTable) {

												if(!empty($this->productMetadata->date_of_sell)){
													$deadlineUtils->setProductDeadlines(DateTime::createFromFormat("Y-m-d", $this->productMetadata->date_of_sell));
												} else {
													$deadlineUtils->insertEmptyProductDeadlines();
												}

											} else {
												$dateOfSell = MetadataServices::getAssetMetadata($assetId)->date_of_sell;
												if($this->productMetadata->date_of_sell != null && ($dateOfSell != $this->productMetadata->date_of_sell)){
													$deadlineUtils->updateAllDeadlines(DateTime::createFromFormat("Y-m-d", $this->productMetadata->date_of_sell));
												}
											}

											// gestione flag copy e traduzioni
											OvsImportServices::saveCopyAndTranslationsCompleteness($this->productMetadata);
										}
									}
								}
							}

                            // Salvataggio dei metadati
							unset($this->productMetadata->translations['otherLang']);
                            OvsImportServices::saveMetadata($this->productMetadata, $isNewProduct);

                            // Salvataggio eans nella tabella product_codes_in_eans
                            OvsProductCodeEanServices::saveProductCodeEans($this->productMetadata->product_code, $this->eans, $this->eansSizeCodes);

                            //Salvataggio Eans e taglie in tabella
							if(!empty($eansAndSizes)){
								OvsProductCodeEanServices::saveProductSizes($this->productMetadata->assetId, $eansAndSizes);
								//AuditUtils::traceAuditEventByEventTypeId($this->productMetadata->assetId, AuditEventTypeEnum);
							}

							// Salvataggio di years and periods nelle tabelle assets_in_channels_in_years_and_periods, years_and_periods, per ogni canale
							if (!empty($this->yearAndPeriodPerChannelValues)){
								OvsYearsAndPeriodsServices::saveYearsAndPeriods($assetId, $this->yearAndPeriodPerChannelValues);
							}


                            $fullXml = $this->fullProductXml;
                            (new PdoDao('common_metadata'))->update(['asset_id'=> $this->productMetadata->assetId], ['pcm_xml' => $fullXml]);
							$hasImgs = false;
							$setStyleModelAsPreviewNonLegacy = true;
							// prova ad agganciare foto da staging area, sia legacy che non
							$hasImgs = self::linkToImages($assetId, OvsUtils::isLegacy($assetId));
							if (!$isNewProduct && (!$legacyMode && !OvsUtils::isLegacy($assetId))) {
								// se il prodotto non è legacy e non è nuovo, non aggiungere style model image come preview
								$setStyleModelAsPreviewNonLegacy = false;
							}
							if(!$hasImgs && $this->productMetadata->model !== null && $setStyleModelAsPreviewNonLegacy){
								//provo a mettere in preview la style image se non ci sono foto prodotto
								StyleUtils::setStyleProductImageAsPreview($assetId, $this->productMetadata);
								StyleUtils::linkProductToModel($assetId, $this->productMetadata->model);
							}

                        }

                        else {
                            //Log event
                            ImportUtils::logEvent($sourceCode, "SAP Hybris Import",  "FAILED", "existent product", "asset locked", $assetId);
                        }
    				} else {
    				    Log::getLogger()->info('XML Import', ['Product#'.$this->productMetadata->erp_id.' is already updated.']);
    				}
                }

    		}catch(Exception $e) {
    		    $errorMessage = $e->getMessage() != null ? $e->getMessage() : "Unknown Error";
    		    $fileWithError = $e->getFile() != null ? $e->getFile() : "Unknown File";
    		    $lineWithError = $e->getLine() != null ? $e->getLine() : "Unknown Line";
    		    $completeErrorMessage = $errorMessage.' in file: '.$fileWithError.' at line: '.$lineWithError;
    		    Log::getLogger()->info('XML Import', ['Error processing product#'.$this->productCounter. ' with productCode: '.$this->productMetadata->erp_id.' Message: '.$completeErrorMessage]);

    		    // Log event
    		    $assetId = isset($assetId) ? $assetId : 0;
    		    ImportUtils::logEvent($sourceCode, "SAP Hybris Import", "FAILED", "Error", $completeErrorMessage, $assetId);
    		}


			unset($this->productMetadata);
			unset($this->taxonomies);

			unset($this->summary);
			$this->summary = array();
			unset($this->description);
			$this->description = array();
			unset($this->colorGroup);
			$this->colorGroup = array();
			unset($this->colorEditorial);
			$this->colorEditorial = array();
 			unset($this->variant2values);
 			$this->variant2values = array();
			unset($this->technicalPlusValues);
			$this->technicalPlusValues = array();
			unset($this->technicalOptionsValues);
			$this->technicalOptionsValues = array();
			unset($this->variant2valuesDisplayNames);
			$this->variant2valuesDisplayNames = array();
			unset($this->eanCodes);
			$this->eanCodes = array();
			unset($this->eansSizeCodes);
			$this->eansSizeCodes = array();
			unset($this->eanSizes);
			$this->eanSizes = array();
			unset($this->channelExternalIds);
			$this->channelExternalIds = array();
			$this->eans = [];
			unset($this->yearAndPeriodPerChannelValues);
			$this->yearAndPeriodPerChannelValues = array();

 			// Mettiamo uno sleep per ridurre il carico complessivo sul sistema quando ci sono N processi attivi
            $sleepTime = Shell::getConfig('pcmImporter.delaySecondsBetweenProducts', 1);

            if ($sleepTime > 0) {
                sleep($sleepTime);
            }
		}

	}

	function image_product_channel_compatible($imageAssetId, $assetId) {
	    $imageToLinkMetadata= MetadataServices::getAssetMetadata($imageAssetId);
		$shotTypeId = $imageToLinkMetadata->image_type[0];
		$shootingMetadata = new StdClass();
		$shootingCategoryId = OvsUtils::getShootingCategoryIdFromShotTypeId($shotTypeId);
		$shootingMetadata->shooting_category = $shootingCategoryId;

		$shootingMetadata->shot_type = $shotTypeId;
		$shootingMetadata->shot_status = ShotStatusEnum::DA_APPROVARE;

		MetadataServices::saveAssetMetadata(
            $imageAssetId,
            $shootingMetadata,
            ['skip_hooks' => true, 'metadata_schema_name' => AdvancedProductWorkflowConstants::SHOOTING_METADATA_SCHEMA_NAME]
        );
	    $channelId = OvsChannelServices::getChannelIdByShotType($shotTypeId)->channel_id;
	    return OvsChannelServices::getAssetInChannel($assetId, $channelId);
	}

	function link_product_image($item, $assetId) {
	    $theassetIds = array();
	    //La tabella potrebbe essere sporca, controllo prima che esista
	    //Potrebbe essere pesante come controllo ed e' quasi superfluo, check.
	    $asset=AssetServices::getAsset($item->asset_id);
	    if ($asset!=null) {
	        $theassetIds[0] = $item->asset_id;
	        ContainerServices::add($theassetIds, $assetId);
	        self::_updateImageOrder($assetId, $item->asset_id);
	    }
	}

	function try_to_link_image_to_product_not_legacy($item, $assetId, $channelId) {
	    $asset=AssetServices::getAsset($item->asset_id);
	    if ($asset!=null) {
			$dao = new PdoDao('asset_links');
			$dao->insert(['src_asset_id' => $item->asset_id, 'dst_asset_id' => $assetId, 'link_type' => 'member_of_in_ch#' . strval($channelId)]);

	        self::_updateImageOrder($assetId, $item->asset_id);
	    }


	}

	function update_image_status_pc ($item, $productCode, $catalogueId) {
	    $metadata = new stdClass();
	    $metadata->linked_to_products = 2;
	    $metadata->catalogue = $catalogueId;
	    //Deve uscire dalla staging?
	    OvsStagingAreaServices::removeImageByAssetIdandPc($item->asset_id, $productCode);
	    $results=OvsStagingAreaServices::getImageAssetIds($item->asset_id);
	    if (empty($results)) {
	        $metadata->notes = null;
	        $metadata->staging_code = LinkingStateErrorCodes::NOT_IN_STAGING;
	    } else {
	        $metadata->notes="Still unassociated codes";
	    }
	    MetadataServices::saveAssetMetadata($item->asset_id, $metadata, [
            'skip_trigger_mam_listener' => true
        ]);
	}

	function update_image_status_ean($item, $ean) {
	    $metadata = new stdClass();
	    $metadata->linked_to_products = 2;
	    //Deve uscire dalla staging?
	    OvsStagingAreaServices::removeImageByAssetIdandEAN($item->asset_id, $ean);
	    $results=OvsStagingAreaServices::getImageAssetIdsByEan($ean);
	    if (empty($results)) {
	        $metadata->notes = null;
	        $metadata->staging_code = LinkingStateErrorCodes::NOT_IN_STAGING;
	    }
	    MetadataServices::saveAssetMetadata($item->asset_id, $metadata, [
            'skip_trigger_mam_listener' => true
        ]);
	}

	private function _updateImageOrder($assetId, $imageAssetId) {
		// TODO: check bug order

	    $imageMetadata = MetadataServices::getAssetMetadata($imageAssetId);
	    $imageOrdinal = $imageMetadata->order;

	    //Controllo che sia inizializzato
	    if ($imageOrdinal == null || $imageOrdinal < 100) {

	        $shotType = $imageMetadata->image_type;
	        $sortOrderWithoutOffset = @TaxonomyServices::getTaxonomyTermById($shotType[0])->sort_order;
	        $imageChannelId = OvsChannelServices::getChannelIdByShotType($shotType[0])->channel_id;
	        $imageChannelGlobalId = ChannelUtils::getChannelById($imageChannelId)->global_id;
	        $sortOrderWithOffset = $sortOrderWithoutOffset;
	        if ($sortOrderWithoutOffset != null) {

	            if ($imageChannelGlobalId == OvsConstants::OVS_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_OVS;
	            } else  if ($imageChannelGlobalId == OvsConstants::UPIM_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_UPIM;
	            } else  if ($imageChannelGlobalId == OvsConstants::ZALANDO_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_ZALANDO;
	            } else if ($imageChannelGlobalId == OvsConstants::SCHOOL_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_SCHOOL;
	            } else if ($imageChannelGlobalId == OvsConstants::STEFANEL_CHANNEL_GLOBAL_ID ) {
                    $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_STEFANEL;
	            } else if ($imageChannelGlobalId == OvsConstants::GAP_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_GAP;
	            } else if ($imageChannelGlobalId == OvsConstants::PIOMBO_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_PIOMBO;
	            } else if ($imageChannelGlobalId == OvsConstants::LES_COPAINS_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_LES_COPAINS;
	            } else if ($imageChannelGlobalId == OvsConstants::AMAZON_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_AMAZON;
	            } else if ($imageChannelGlobalId == OvsConstants::CROFF_CHANNEL_GLOBAL_ID ) {
	                $sortOrderWithOffset = $sortOrderWithOffset + OvsConstants::OFFSET_CROFF;
	            }
	        }

	        $imageOrdinal = $sortOrderWithOffset;
	    }

	    $dao = new PdoDao('asset_links');
	    $dao->update(['src_asset_id' => $imageAssetId, 'dst_asset_id' => $assetId], ['ordinal' => $imageOrdinal]);

	}



	public static function _checkIsUpdated($assetId, $xmlYear) {
	    $isUpdated = false;
	    $pdo = new PdoDao('common_metadata');
	    $res = $pdo->getOneByFilter('asset_id = ?',[$assetId],'year');

	    if(!empty($res)) {
	        $assetYear = $res->year;

	        if($xmlYear < $assetYear) {
	            $isUpdated = true;
	        }
 	    }
	    return $isUpdated;
	}

	function buildStartTagAndAttributes($tag, $attributes) {
	    $returnTag = "<$tag";

	    foreach ($attributes as $attributeKey => $attributeValue) {
	        $attributeValue = html_entity_decode($attributeValue);
	        $returnTag .= " $attributeKey='$attributeValue'";
	    }

	    $returnTag .= ">";

	    return $returnTag;
	}

	function buildEndTagAndAttributes($tag) {
	    $returnTag = "</$tag>";

	    return $returnTag;
	}

	private function linkToImages($assetId, $isLegacy): bool{
		// Provo ad agganciare le foto di staging area a questo prodotto
		$hasImgs = false;
		$records= OvsStagingAreaServices::getImageAssetIdsByProductCode($this->productMetadata->product_code);

		foreach ($records as $item) {
			$assetInChannel = self::image_product_channel_compatible($item->asset_id, $assetId);

			if (!$assetInChannel==false && $assetInChannel->status != ApprovalStatusEnum::PRODUCT_PUBLISHED_STATUS_NAME) {
				if ($isLegacy){
					self::link_product_image($item, $assetId);
				} else {
					self::try_to_link_image_to_product_not_legacy($item, $assetId, $assetInChannel->channel_id);
				}
				self::update_image_status_pc($item, $this->productMetadata->product_code, $this->catalogueId);
				$hasImgs = true;
			}
		}

		// Per EAN
		$normalizedEANS = str_replace(" ", "-", $this->productMetadata->ean);
		$EANArray = explode("-", $normalizedEANS);

		foreach ($EANArray as $checkEAN) {
			$records= OvsStagingAreaServices::getImageAssetIdsByEan($checkEAN);

			foreach ($records as $item) {
				$assetInChannel = self::image_product_channel_compatible($item->asset_id, $assetId);

				if (!$assetInChannel==false && $assetInChannel->status != ApprovalStatusEnum::PRODUCT_PUBLISHED_STATUS_NAME) {
					if ($isLegacy){
						self::link_product_image($item, $assetId);
					} else {
						self::try_to_link_image_to_product_not_legacy($item, $assetId, $assetInChannel->channel_id);
					}
					self::update_image_status_ean($item, $checkEAN);
					$hasImgs = true;
				}
			}
		}

		return $hasImgs;
	}

	function manageTaxonomyTerms($termDescription, $taxName, $metadataField){

		if ($termDescription != null && $termDescription != "") {
			$taxonomy = new stdClass();
			$taxonomy->taxName = $taxName;
			$taxonomy->metadataField = $metadataField;
			$taxonomy->termDescription = $termDescription;
			array_push($this->taxonomies, $taxonomy);
		}
	}

	function manageAttributeTag( $attributes){
		switch ($attributes['name']) {
			case 'hostDescription':
				$this->productMetadata->title = $attributes['value'];
				break;
			case 'year':
				$this->productMetadata->year = $attributes['value'];
				break;
			case 'period':
				$this->productMetadata->period = $attributes['value'];
				break;
			case 'shootingYear':
				$this->productMetadata->shootingYear = $attributes['value'];
				break;
			case 'shootingPeriod':
				$this->productMetadata->shootingPeriod = $attributes['value'];
				break;
			case 'heightOpen':
				$this->productMetadata->height_open = $attributes['value'];
				break;
			case 'heightClose':
				$this->productMetadata->height_close = $attributes['value'];
				break;
			case 'widthOpen':
				$this->productMetadata->width_open = $attributes['value'];
				break;
			case 'widthClose':
				$this->productMetadata->width_close = $attributes['value'];
				break;
			case 'depthOpen':
				$this->productMetadata->depth_open = $attributes['value'];
				break;
			case 'depthClose':
				$this->productMetadata->depth_close = $attributes['value'];
				break;
			case 'diameterOpen':
				$this->productMetadata->diameter_open = $attributes['value'];
				break;
			case 'diameterClose':
				$this->productMetadata->diameter_close = $attributes['value'];
				break;
			case 'grossWeight':
				$this->productMetadata->gross_weight = $attributes['value'];
				break;
			case 'netWeight':
				$this->productMetadata->net_weight = $attributes['value'];
				break;
			case 'model':
				$this->productMetadata->model = $attributes['value'];
				break;
			case 'subDepartmentDescription':
				$this->productMetadata->sub_department = $attributes['value'];
				break;
			case 'subDepartment':
				$this->productMetadata->sub_department_id = $attributes['value'];
				break;
			case 'dateOfSellHOST':
				$this->productMetadata->date_of_sell = $attributes['value'];
				break;
			case 'ricondizionato':
				$this->productMetadata->recon = $attributes['value'];
				break;
			case 'brandDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Brand', 'brand');
				break;
			case 'category':
				$this->productMetadata->category_id = $attributes['value'];
				break;
			case 'categoryDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Category', 'productCategory');
				break;
			case 'department':
				$this->productMetadata->department_id = $attributes['value'];
				break;
			case 'departmentDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Department', 'department');
				break;
			case 'function':
				$this->productMetadata->function_id = $attributes['value'];
				break;
			case 'sectorCode':
				$this->productMetadata->sector_id = $attributes['value'];
				break;
			case 'functionDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Function', 'function');
				break;
			case 'colorHost':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Color', 'color');
				break;
			case 'season':
				$this->productMetadata->season_id = $attributes['value'];
				break;
			case 'seasonDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Season', 'season');
				break;
			case 'summary':
				$this->currentAttribute = 'summary';
				break;
			case 'description':
				$this->currentAttribute = 'description';
				break;
			case 'colorGroup':
				$this->currentAttribute = 'colorGroup';
				$this->productMetadata->colorGroupCode = $attributes['value'];
				break;
			case 'colorEditorial':
				$this->currentAttribute = 'colorEditorial';
				break;

			case 'technicalPlus':
				$this->currentAttribute = 'technicalPlus';
				break;

			case 'technicalOptions':
				$this->isTechnicalOptions = true;
				unset($this->currentAttribute);
				break;
			case 'IsCustomizable':
				$this->productMetadata->is_customizable = $attributes['value'] == 'true' ? 1 : 0;
				break;
			case 'copyIT':
				$this->productMetadata->copyComplete = $attributes['value'] == 'true' ? true : false;
				break;
			case 'translateEN':
				$this->productMetadata->translationsComplete = $attributes['value'] == 'true' ? true : false;
				break;
			case 'no_ecommerce':
				$this->productMetadata->no_ecommerce = $attributes['value'] == 'true' ? true : false;
				break;
			case 'composition':
				$this->productMetadata->composition = $attributes['value'];
				break;
			case 'genderDescription':
				self::manageTaxonomyTerms($attributes['value'], 'Product_Gender', 'gender');
			default:
				// Handle other attribute names if needed
				break;
		}
	}
}
?>